const fs = require('fs');
const path = require('path');
const { createCanvas } = require('canvas');

// Ensure the OG images directory exists
const ogImagesDir = path.join(__dirname, '../public/images/og');
if (!fs.existsSync(ogImagesDir)) {
  fs.mkdirSync(ogImagesDir, { recursive: true });
}

// Function to generate an OG image
function generateOGImage(options) {
  const {
    filename,
    title,
    subtitle,
    companyName = 'Advisync Solutions',
    width = 1200,
    height = 630,
    primaryColor = '#6366F1',
    secondaryColor = '#22D3EE',
    backgroundColor = '#030712',
    backgroundGradientMiddle = '#111827',
    textColor = '#F9FAFB',
    subtitleColor = '#9CA3AF'
  } = options;

  // Create canvas
  const canvas = createCanvas(width, height);
  const ctx = canvas.getContext('2d');

  // Background gradient
  const gradient = ctx.createLinearGradient(0, 0, width, height);
  gradient.addColorStop(0, backgroundColor);
  gradient.addColorStop(0.5, backgroundGradientMiddle);
  gradient.addColorStop(1, backgroundColor);
  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, width, height);

  // Add decorative elements
  ctx.fillStyle = primaryColor;
  ctx.globalAlpha = 0.1;
  ctx.beginPath();
  ctx.arc(width - 100, 100, 300, 0, Math.PI * 2);
  ctx.fill();

  ctx.fillStyle = secondaryColor;
  ctx.globalAlpha = 0.1;
  ctx.beginPath();
  ctx.arc(100, height - 130, 250, 0, Math.PI * 2);
  ctx.fill();

  // Reset opacity
  ctx.globalAlpha = 1;

  // Add border
  ctx.strokeStyle = primaryColor;
  ctx.lineWidth = 4;
  ctx.strokeRect(20, 20, width - 40, height - 40);

  // Add title
  ctx.fillStyle = textColor;
  ctx.font = `bold ${Math.floor(width / 20)}px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;
  ctx.textAlign = 'center';
  ctx.fillText(title, width / 2, height / 2 - 50);

  // Add subtitle
  ctx.fillStyle = subtitleColor;
  ctx.font = `${Math.floor(width / 37.5)}px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;
  ctx.fillText(subtitle, width / 2, height / 2 + 20);

  // Add company name
  ctx.fillStyle = primaryColor;
  ctx.font = `bold ${Math.floor(width / 30)}px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`;
  ctx.fillText(companyName, width / 2, height - 130);

  // Add icon placeholders
  function drawIcon(x, y, size) {
    ctx.fillStyle = primaryColor;
    ctx.globalAlpha = 0.2;
    ctx.beginPath();
    ctx.arc(x, y, size, 0, Math.PI * 2);
    ctx.fill();
    ctx.globalAlpha = 1;
    ctx.strokeStyle = primaryColor;
    ctx.lineWidth = 2;
    ctx.stroke();
  }

  const iconY = height - 230;
  const iconSpacing = width / 6;
  for (let i = 1; i <= 5; i++) {
    drawIcon(i * iconSpacing, iconY, 30);
  }

  // Save the image
  const buffer = canvas.toBuffer('image/jpeg', { quality: 0.9 });
  fs.writeFileSync(path.join(ogImagesDir, filename), buffer);
  console.log(`Generated OG image: ${filename}`);
}

// Generate NDIS service OG image
generateOGImage({
  filename: 'ndis-services-og.jpg',
  title: 'NDIS Provider Solutions',
  subtitle: 'Specialized Automation for Melbourne NDIS Providers'
});

// Generate other OG images
generateOGImage({
  filename: 'services-og.jpg',
  title: 'Our Services',
  subtitle: 'Digital Automation & Web Solutions for Melbourne Businesses'
});

generateOGImage({
  filename: 'case-studies-og.jpg',
  title: 'Case Studies',
  subtitle: 'Real Results for Melbourne Businesses'
});

generateOGImage({
  filename: 'blog-og.jpg',
  title: 'Blog & Insights',
  subtitle: 'Expert Digital Automation Tips & Strategies'
});

generateOGImage({
  filename: 'consultation-og.jpg',
  title: 'Free Consultation',
  subtitle: 'Book Your Digital Automation Strategy Session'
});

generateOGImage({
  filename: 'home-og.jpg',
  title: 'Advisync Solutions',
  subtitle: 'Melbourne\'s Leading Digital Automation Agency'
});

console.log('All OG images generated successfully!'); 