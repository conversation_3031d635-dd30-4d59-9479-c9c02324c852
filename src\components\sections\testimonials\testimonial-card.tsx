'use client';

import Image from 'next/image';
import { motion } from 'framer-motion';
import { Star } from 'lucide-react';

interface TestimonialCardProps {
  name: string;
  role: string;
  company: string;
  companyLogo: string;
  content: string;
  rating: number;
  image?: string;
}

export function TestimonialCard({
  name,
  role,
  company,
  companyLogo,
  content,
  rating,
  image
}: TestimonialCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      className="bg-secondary/5 backdrop-blur-sm rounded-2xl p-6 border border-primary/10 relative overflow-hidden group"
    >
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

      {/* Content Container */}
      <div className="relative">
        {/* Quote Icon */}
        <div className="absolute -top-2 -left-2 text-primary/20 transform -rotate-12">
          <svg
            width="32"
            height="32"
            viewBox="0 0 24 24"
            fill="currentColor"
          >
            <path d="M4.583 17.321C3.553 16.227 3 15 3 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179zm10 0C13.553 16.227 13 15 13 13.011c0-3.5 2.457-6.637 6.03-8.188l.893 1.378c-3.335 1.804-3.987 4.145-4.247 5.621.537-.278 1.24-.375 1.929-.311 1.804.167 3.226 1.648 3.226 3.489a3.5 3.5 0 01-3.5 3.5c-1.073 0-2.099-.49-2.748-1.179z" />
          </svg>
        </div>

        {/* Testimonial Content */}
        <blockquote className="text-foreground/80 mb-4 mt-4">
          {content}
        </blockquote>

        {/* Author Info */}
        <div className="flex items-center gap-4">
          {image && (
            <div className="relative h-12 w-12 rounded-full overflow-hidden border-2 border-primary/20">
              <Image
                src={image}
                alt={name}
                fill
                className="object-cover"
              />
            </div>
          )}
          
          <div>
            <h4 className="font-semibold text-foreground">{name}</h4>
            <p className="text-sm text-foreground/60">
              {role} at {company}
            </p>
          </div>
        </div>

        {/* Rating */}
        <div className="flex items-center gap-1 mt-4">
          {Array.from({ length: rating }).map((_, i) => (
            <Star
              key={i}
              size={16}
              className="fill-accent-secondary text-accent-secondary"
            />
          ))}
        </div>

        {/* Company Logo */}
        <div className="absolute top-6 right-6">
          <div className="relative h-8 w-20">
            <Image
              src={companyLogo}
              alt={company}
              fill
              className="object-contain opacity-50 group-hover:opacity-80 transition-opacity"
            />
          </div>
        </div>
      </div>
    </motion.div>
  );
} 