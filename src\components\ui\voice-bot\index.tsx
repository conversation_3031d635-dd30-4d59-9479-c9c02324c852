'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Mic, MicOff, Volume2, VolumeX } from 'lucide-react';
import { cn } from '@/lib/utils';

interface VoiceBotProps {
  publicKey?: string;
  assistantId?: string;
  autoplay?: boolean;
  maxDuration?: number; // in seconds
  className?: string;
}

export function VoiceBot({
  publicKey = process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY || 'YOUR_PUBLIC_KEY',
  assistantId = process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID || 'YOUR_ASSISTANT_ID',
  autoplay = false,
  maxDuration = 60,
  className
}: VoiceBotProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const [isListening, setIsListening] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const startTimeRef = useRef<number | null>(null);

  useEffect(() => {
    // Load Vapi script
    const script = document.createElement('script');
    script.src = 'https://cdn.vapi.ai/web-sdk.js';
    script.async = true;
    script.onload = () => {
      setIsLoaded(true);
      initializeVapi();
    };
    script.onerror = () => setIsError(true);
    document.body.appendChild(script);

    return () => {
      // Clean up script and timer on unmount
      if (document.body.contains(script)) {
        document.body.removeChild(script);
      }
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  const initializeVapi = () => {
    if (typeof window !== 'undefined' && window.Vapi) {
      try {
        window.Vapi.configure({
          publicKey,
          assistantId,
          enableLogging: process.env.NODE_ENV === 'development',
          onCallStarted: handleCallStarted,
          onCallEnded: handleCallEnded,
        });

        if (autoplay) {
          // Start call automatically after a short delay
          setTimeout(() => {
            startCall();
          }, 1000);
        }
      } catch (error) {
        console.error('Error initializing Vapi:', error);
        setIsError(true);
      }
    }
  };

  const startCall = () => {
    if (typeof window !== 'undefined' && window.Vapi) {
      try {
        window.Vapi.start();
        setIsListening(true);
        startTimeRef.current = Date.now();

        // Set timer to end call after maxDuration
        if (maxDuration > 0) {
          timerRef.current = setTimeout(() => {
            endCall();
          }, maxDuration * 1000);
        }
      } catch (error) {
        console.error('Error starting Vapi call:', error);
      }
    }
  };

  const endCall = () => {
    if (typeof window !== 'undefined' && window.Vapi) {
      try {
        window.Vapi.stop();
        setIsListening(false);
        startTimeRef.current = null;

        if (timerRef.current) {
          clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      } catch (error) {
        console.error('Error ending Vapi call:', error);
      }
    }
  };

  const toggleMute = () => {
    if (typeof window !== 'undefined' && window.Vapi) {
      try {
        if (isMuted) {
          window.Vapi.unmute();
        } else {
          window.Vapi.mute();
        }
        setIsMuted(!isMuted);
      } catch (error) {
        console.error('Error toggling mute:', error);
      }
    }
  };

  const handleCallStarted = () => {
    setIsListening(true);
    startTimeRef.current = Date.now();
  };

  const handleCallEnded = () => {
    setIsListening(false);
    startTimeRef.current = null;
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  };

  const getRemainingTime = () => {
    if (!startTimeRef.current) return maxDuration;
    const elapsedSeconds = Math.floor((Date.now() - startTimeRef.current) / 1000);
    return Math.max(0, maxDuration - elapsedSeconds);
  };

  return (
    <div className={cn("flex flex-col items-center", className)}>
      {isError ? (
        <div className="p-6 bg-background/50 border border-red-500/20 rounded-xl text-center">
          <p className="text-red-400 mb-2">Unable to load the voice assistant.</p>
          <p className="text-foreground/70 text-sm">Please check your connection or try again later.</p>
        </div>
      ) : (
        <div className="flex flex-col items-center gap-4">
          <div className="relative">
            <motion.div
              animate={isListening ? { scale: [1, 1.1, 1], opacity: [0.7, 1, 0.7] } : {}}
              transition={{ repeat: Infinity, duration: 2 }}
              className={cn(
                "absolute -inset-4 rounded-full",
                isListening ? "bg-primary/20" : "bg-transparent"
              )}
            />
            <motion.button
              onClick={isListening ? endCall : startCall}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "relative z-10 w-16 h-16 rounded-full flex items-center justify-center shadow-lg transition-colors border",
                isListening
                  ? "bg-red-500 text-white shadow-red-500/40 hover:bg-red-600 border-red-400/30"
                  : "bg-primary text-white shadow-primary/40 hover:bg-primary/90 border-primary/30"
              )}
            >
              {isListening ? <MicOff size={24} /> : <Mic size={24} />}
              <div className={cn(
                "absolute inset-0 rounded-full blur-md -z-10",
                isListening ? "bg-red-500/20" : "bg-primary/20"
              )}></div>
            </motion.button>
          </div>

          {isListening && (
            <div className="flex items-center gap-4">
              <button
                onClick={toggleMute}
                className={cn(
                  "p-2 rounded-full transition-colors",
                  isMuted
                    ? "bg-red-500/10 text-red-500 hover:bg-red-500/20"
                    : "bg-primary/10 text-primary hover:bg-primary/20"
                )}
              >
                {isMuted ? <VolumeX size={18} /> : <Volume2 size={18} />}
              </button>

              {maxDuration > 0 && (
                <div className="text-sm text-foreground/70">
                  {getRemainingTime()}s remaining
                </div>
              )}
            </div>
          )}

          <p className="text-sm text-foreground/60 text-center max-w-xs">
            {isListening
              ? "Speak now. The AI assistant is listening..."
              : "Click the microphone to start talking with our AI assistant."}
          </p>
        </div>
      )}
    </div>
  );
}

// Add type definition for Vapi
declare global {
  interface Window {
    Vapi?: {
      configure: (options: {
        publicKey: string;
        assistantId: string;
        enableLogging?: boolean;
        onCallStarted?: () => void;
        onCallEnded?: () => void;
      }) => void;
      start: () => void;
      stop: () => void;
      mute: () => void;
      unmute: () => void;
    };
  }
}
