'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';

interface PreloaderContextType {
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  showPreloader: () => void;
  hidePreloader: () => void;
}

const PreloaderContext = createContext<PreloaderContextType | undefined>(undefined);

// Use sessionStorage to track if this is the first visit in this browser session
const isFirstVisitInSession = () => {
  // Always return true during server-side rendering
  if (typeof window === 'undefined') return true;
  
  try {
    const visited = sessionStorage.getItem('advisync_session_visited');
    if (!visited) {
      sessionStorage.setItem('advisync_session_visited', 'true');
      return true;
    }
    return false;
  } catch (error) {
    // If sessionStorage is not available, always show preloader
    console.error('Error accessing sessionStorage:', error);
    return true;
  }
};

export function PreloaderProvider({ children }: { children: React.ReactNode }) {
  const [isLoading, setIsLoading] = useState(false);
  const [isBrowser, setIsBrowser] = useState(false);
  const pathname = usePathname();
  
  // Set isBrowser to true once component mounts
  useEffect(() => {
    setIsBrowser(true);
  }, []);
  
  // Show preloader on home page for each new browser session
  useEffect(() => {
    // Skip during server-side rendering
    if (!isBrowser) return;
    
    // Only show preloader on home page (root path)
    const isHomePage = pathname === '/';
    
    // Check if this is the first visit in this browser session
    const isFirstVisit = isFirstVisitInSession();
    
    // Show preloader if this is the home page and first visit in this session
    if (isHomePage && isFirstVisit) {
      setIsLoading(true);
      
      // Handle page load event
      const handleLoad = () => {
        // After page is fully loaded, wait before hiding preloader
        setTimeout(() => {
          setIsLoading(false);
        }, 3000); // 3 second delay for a smooth experience
      };

      if (document.readyState === 'complete') {
        handleLoad();
      } else {
        window.addEventListener('load', handleLoad);
        return () => window.removeEventListener('load', handleLoad);
      }
    } else {
      // Don't show preloader for other pages or subsequent visits
      setIsLoading(false);
    }
  }, [pathname, isBrowser]);

  const showPreloader = () => setIsLoading(true);
  const hidePreloader = () => setIsLoading(false);

  return (
    <PreloaderContext.Provider value={{ isLoading, setIsLoading, showPreloader, hidePreloader }}>
      {children}
    </PreloaderContext.Provider>
  );
}

export function usePreloader() {
  const context = useContext(PreloaderContext);
  if (context === undefined) {
    throw new Error('usePreloader must be used within a PreloaderProvider');
  }
  return context;
} 