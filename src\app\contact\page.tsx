import type { Metadata } from 'next';
import { PageHeader } from '@/components/common/page-header';
import { ContactForm } from '@/components/sections/contact-form';
import { Container } from '@/components/common/container';
import { MapPin } from 'lucide-react';
import { contactInfo, socialMedia } from '@/config/contact';

export const metadata: Metadata = {
  title: 'Contact Us | Advisync Solutions - Melbourne Business Automation Experts',
  description: 'Get in touch with Advisync Solutions for innovative business automation, web development, and AI solutions. Serving Melbourne small businesses and NDIS providers with cutting-edge digital transformation.',
  keywords: [
    'business automation Melbourne',
    'small business solutions',
    'NDIS provider automation',
    'digital transformation Melbourne',
    'web development Melbourne',
    'AI solutions Melbourne',
    'workflow automation',
    'business process automation',
    'Melbourne tech consultancy',
    'small business automation',
    'NDIS digital solutions',
    'business efficiency solutions'
  ],
  openGraph: {
    title: 'Contact Advisync Solutions - Melbourne Business Automation Experts',
    description: 'Transform your business with our automation, web development, and AI solutions. Specialized expertise for Melbourne small businesses and NDIS providers.',
    url: 'https://advisync.com.au/contact',
    siteName: 'Advisync Solutions',
    locale: 'en_AU',
    type: 'website',
  },
};

// Structured data for SEO
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'ContactPage',
  name: 'Advisync Solutions Contact Page',
  description: 'Contact Advisync Solutions for business automation, web development, and AI solutions. Serving Melbourne small businesses and NDIS providers.',
  url: 'https://advisync.com.au/contact',
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: contactInfo.phone,
    contactType: 'customer service',
    areaServed: [
      {
        '@type': 'City',
        name: 'Melbourne',
        '@id': 'https://www.wikidata.org/wiki/Q3141'
      }
    ],
    availableLanguage: 'English',
    email: contactInfo.email,
    hoursAvailable: contactInfo.hours
  },
  address: {
    '@type': 'PostalAddress',
    addressLocality: contactInfo.address.locality,
    addressRegion: contactInfo.address.region,
    addressCountry: contactInfo.address.country,
  },
  potentialAction: {
    '@type': 'ContactAction',
    target: {
      '@type': 'EntryPoint',
      urlTemplate: 'https://advisync.com.au/contact',
      inLanguage: 'en-AU',
      actionPlatform: [
        'http://schema.org/DesktopWebPlatform',
        'http://schema.org/MobileWebPlatform'
      ]
    },
    result: {
      '@type': 'Message',
      description: 'Business automation and digital transformation inquiry'
    }
  },
  serviceType: [
    'Business Process Automation',
    'Web Development',
    'AI Solutions',
    'Digital Transformation',
    'NDIS Provider Solutions',
    'Workflow Automation',
    'Small Business Technology'
  ]
};

export default function ContactPage() {
  return (
    <>
      {/* Add JSON-LD structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Hero Section */}
      <PageHeader
        title="Contact Us"
        description="Have a question or ready to start your next project? Get in touch with our team and we'll get back to you within 24 hours."
        className="pb-8"
      />

      <Container className="py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Form */}
          <div>
            <h2 className="text-2xl font-bold mb-6">Send Us a Message</h2>
            <ContactForm />
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-6">Contact Information</h2>
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-2">Location</h3>
                  <p className="text-foreground/70">
                    {contactInfo.address.locality}, {contactInfo.address.region}<br />
                    {contactInfo.address.country}
                  </p>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Email</h3>
                  <a 
                    href={`mailto:${contactInfo.email}`}
                    className="text-primary hover:underline"
                  >
                    {contactInfo.email}
                  </a>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Phone</h3>
                  <a 
                    href={`tel:${contactInfo.phone}`}
                    className="text-primary hover:underline"
                  >
                    {contactInfo.phone}
                  </a>
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-2">Support Hours</h3>
                  <p className="text-foreground/70">
                    {contactInfo.hours}
                  </p>
                </div>
              </div>
            </div>

            {/* Social Media Links */}
            <div>
              <h2 className="text-2xl font-bold mb-6">Connect With Us</h2>
              <div className="flex flex-wrap gap-4">
                <a 
                  href={socialMedia.facebook.url}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-facebook"><path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z" /></svg>
                  Facebook
                </a>
                <a 
                  href={socialMedia.instagram.url}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-purple-500 via-pink-500 to-orange-500 text-white rounded-md hover:opacity-90 transition-opacity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-instagram"><rect width="20" height="20" x="2" y="2" rx="5" ry="5"/><path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"/><line x1="17.5" x2="17.51" y1="6.5" y2="6.5"/></svg>
                  Instagram
                </a>
                <a 
                  href={socialMedia.twitter.url}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-twitter"><path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z" /></svg>
                  X (Twitter)
                </a>
                <a 
                  href={socialMedia.linkedin.url}
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 px-4 py-2 bg-blue-700 text-white rounded-md hover:bg-blue-800 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-linkedin"><path d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"/><rect width="4" height="12" x="2" y="9"/><circle cx="4" cy="4" r="2"/></svg>
                  LinkedIn
                </a>
              </div>
              <p className="mt-4 text-foreground/70">
                Follow us on social media for the latest updates, tips, and insights about business automation and digital solutions.
              </p>
            </div>

      
          </div>
        </div>
      </Container>
    </>
  );
} 