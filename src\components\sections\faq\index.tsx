'use client';

import { Container } from "@/components/common/container";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/common/accordion";
import { motion } from "framer-motion";

const faqs = [
  {
    question: "How can Advisync help my small business?",
    answer: "We're here to make your life easier! We specialize in simple but powerful digital solutions that help small business owners like you save time and reduce stress. From automating your daily tasks to creating a beautiful website that brings in customers, we offer friendly, easy-to-use solutions that won't break the bank. Think of us as your local tech partner, helping you grow your business with the right digital tools."
  },
  {
    question: "I'm not tech-savvy - will I be able to use these solutions?",
    answer: "Absolutely! We specifically design our solutions to be user-friendly and straightforward. Whether you're managing appointments, handling customer emails, or updating your website, we make everything simple and intuitive. Plus, we're always just a phone call away if you need help. We believe technology should make your life easier, not more complicated."
  },
  {
    question: "Why choose Advisync for my local business?",
    answer: "We're a small Melbourne-based team who truly understands local small businesses. Unlike big agencies, we take the time to get to know you and your business personally. We speak in plain English (no tech jargon!), offer flexible solutions that grow with your business, and provide the kind of friendly, personal support you'd expect from a local partner."
  },
  {
    question: "Who do you typically work with?",
    answer: "We love working with Melbourne's small business owners and sole traders! Whether you're a local café owner, a freelance designer, a tradesperson, a beauty therapist, or running any other small business, we're here to help. Our solutions are perfect for busy business owners who want to spend less time on admin and more time doing what they love."
  },
  {
    question: "How do you make sure the process is smooth and stress-free?",
    answer: "We keep things simple and work at your pace. First, we'll have a friendly chat about your business over coffee. Then, we'll suggest practical solutions that fit your needs and budget. We implement changes step by step, making sure you're comfortable every step of the way. No rushing, no pressure - just steady progress toward making your business life easier."
  },
  {
    question: "What happens if I need help along the way?",
    answer: "We're always here for you! As a local Melbourne team, we provide personal, friendly support whenever you need it. Whether it's a quick phone call, a video chat, or even a visit to your business, we're happy to help. We also offer simple training sessions and easy-to-follow guides, ensuring you feel confident using your new tools."
  }
];

export const FAQ = () => {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 relative overflow-hidden">
      <Container>
        <div className="max-w-7xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-center mb-8 md:mb-12 px-4"
          >
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-foreground to-accent-secondary mb-3 md:mb-4">
              Questions? We're Here to Help!
            </h2>
            <p className="text-muted-foreground text-base sm:text-lg max-w-2xl mx-auto">
              Get to know how we can help your small business thrive with friendly, practical digital solutions that make your life easier.
            </p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-accent/50 rounded-lg p-4 sm:p-5 md:p-6 backdrop-blur-sm border border-accent mx-4 sm:mx-6 md:mx-auto"
          >
            <Accordion type="single" collapsible className="w-full divide-y divide-accent">
              {faqs.map((faq, index) => (
                <AccordionItem key={index} value={`item-${index}`}>
                  <AccordionTrigger className="text-left text-base sm:text-lg font-medium hover:text-accent-secondary py-3 md:py-4">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-sm sm:text-base text-muted-foreground">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </motion.div>
        </div>
      </Container>
    </section>
  );
};

export default FAQ; 