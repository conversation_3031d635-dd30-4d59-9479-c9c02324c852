'use client';

import { useEffect } from 'react';

interface PerformanceMetrics {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
}

export function PerformanceMonitor() {
  useEffect(() => {
    // Only run on client side and in production
    if (typeof window === 'undefined' || process.env.NODE_ENV !== 'production') {
      return;
    }

    const metrics: PerformanceMetrics = {};

    // Measure Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime;
            break;
          case 'first-input':
            metrics.fid = (entry as any).processingStart - entry.startTime;
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as any).value;
            }
            break;
          case 'paint':
            if (entry.name === 'first-contentful-paint') {
              metrics.fcp = entry.startTime;
            }
            break;
          case 'navigation':
            const navEntry = entry as PerformanceNavigationTiming;
            metrics.ttfb = navEntry.responseStart - navEntry.requestStart;
            break;
        }
      }
    });

    // Observe different entry types
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
      observer.observe({ entryTypes: ['first-input'] });
      observer.observe({ entryTypes: ['layout-shift'] });
      observer.observe({ entryTypes: ['paint'] });
      observer.observe({ entryTypes: ['navigation'] });
    } catch (e) {
      // Some browsers might not support all entry types
      console.warn('Performance monitoring not fully supported:', e);
    }

    // Send metrics after page load
    const sendMetrics = () => {
      if (Object.keys(metrics).length > 0) {
        // In production, you would send this to your analytics service
        console.log('Performance Metrics:', metrics);

        // Example: Send to Google Analytics 4
        if (typeof window !== 'undefined' && 'gtag' in window) {
          Object.entries(metrics).forEach(([metric, value]) => {
            if (value !== undefined) {
              (window as any).gtag('event', 'web_vital', {
                name: metric,
                value: Math.round(value),
                event_category: 'Web Vitals'
              });
            }
          });
        }
      }
    };

    // Send metrics after a delay to ensure all measurements are captured
    const timer = setTimeout(sendMetrics, 5000);

    return () => {
      observer.disconnect();
      clearTimeout(timer);
    };
  }, []);

  return null; // This component doesn't render anything
}

// Helper function to get performance score
export function getPerformanceScore(metrics: PerformanceMetrics): number {
  let score = 100;

  // LCP scoring (Good: <2.5s, Needs Improvement: 2.5-4s, Poor: >4s)
  if (metrics.lcp) {
    if (metrics.lcp > 4000) score -= 30;
    else if (metrics.lcp > 2500) score -= 15;
  }

  // FID scoring (Good: <100ms, Needs Improvement: 100-300ms, Poor: >300ms)
  if (metrics.fid) {
    if (metrics.fid > 300) score -= 25;
    else if (metrics.fid > 100) score -= 10;
  }

  // CLS scoring (Good: <0.1, Needs Improvement: 0.1-0.25, Poor: >0.25)
  if (metrics.cls) {
    if (metrics.cls > 0.25) score -= 25;
    else if (metrics.cls > 0.1) score -= 10;
  }

  // FCP scoring (Good: <1.8s, Needs Improvement: 1.8-3s, Poor: >3s)
  if (metrics.fcp) {
    if (metrics.fcp > 3000) score -= 15;
    else if (metrics.fcp > 1800) score -= 5;
  }

  // TTFB scoring (Good: <800ms, Needs Improvement: 800-1800ms, Poor: >1800ms)
  if (metrics.ttfb) {
    if (metrics.ttfb > 1800) score -= 5;
    else if (metrics.ttfb > 800) score -= 2;
  }

  return Math.max(0, score);
}
