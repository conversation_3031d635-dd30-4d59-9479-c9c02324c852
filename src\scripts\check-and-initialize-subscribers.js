const fs = require('fs');
const path = require('path');

// Path to subscriber data file
const subscribersPath = path.join(process.cwd(), 'subscribers.json');

/**
 * Initializes or checks the subscribers.json file to ensure it has the correct format
 */
function initializeSubscribersFile() {
  console.log('Checking subscribers file...');
  console.log('Path:', subscribersPath);
  
  try {
    let subscribers = [];
    
    // Check if file exists
    if (fs.existsSync(subscribersPath)) {
      try {
        // Try to read the file
        const fileData = fs.readFileSync(subscribersPath, 'utf8');
        console.log('File exists, reading content...');
        
        // Handle empty file
        if (!fileData.trim()) {
          console.log('File is empty, will create new array');
          subscribers = [];
        } else {
          // Try to parse as JSON
          try {
            const parsed = JSON.parse(fileData);
            
            // Check if it's an array
            if (Array.isArray(parsed)) {
              console.log('Valid subscribers array found with', parsed.length, 'subscribers');
              subscribers = parsed;
              
              // Verify each subscriber has the required fields
              let needsRepair = false;
              const repairedSubscribers = subscribers.map(subscriber => {
                const repaired = { ...subscriber };
                
                // Check email field
                if (!repaired.email) {
                  console.log('Found subscriber without email, skipping', repaired);
                  needsRepair = true;
                  return null;
                }
                
                // Add subscribedAt if missing
                if (!repaired.subscribedAt) {
                  console.log('Adding missing subscribedAt for', repaired.email);
                  repaired.subscribedAt = new Date().toISOString();
                  needsRepair = true;
                }
                
                return repaired;
              }).filter(Boolean); // Remove null entries
              
              if (needsRepair) {
                console.log('Repaired subscriber list, now contains', repairedSubscribers.length, 'valid subscribers');
                subscribers = repairedSubscribers;
              }
            } else {
              console.log('File does not contain an array, will create new array');
              subscribers = [];
            }
          } catch (parseError) {
            console.error('Error parsing JSON:', parseError.message);
            console.log('Will create new array');
            subscribers = [];
          }
        }
      } catch (readError) {
        console.error('Error reading file:', readError.message);
        console.log('Will create new array');
        subscribers = [];
      }
    } else {
      console.log('File does not exist, will create it');
    }
    
    // Write the subscribers array back to the file
    fs.writeFileSync(subscribersPath, JSON.stringify(subscribers, null, 2));
    console.log('Successfully wrote subscribers file with', subscribers.length, 'subscribers');
    
    console.log('Sample subscriber format:');
    console.log(JSON.stringify({
      email: '<EMAIL>',
      subscribedAt: new Date().toISOString()
    }, null, 2));
    
    return { success: true, message: 'Subscribers file initialized', count: subscribers.length };
  } catch (error) {
    console.error('Failed to initialize subscribers file:', error);
    return { success: false, message: error.message };
  }
}

// Run if directly executed
if (require.main === module) {
  const result = initializeSubscribersFile();
  console.log('Result:', result);
}

module.exports = { initializeSubscribersFile }; 