import { Metadata } from 'next';

export const siteConfig = {
  name: 'Advisync AI Solutions',
  url: 'https://advisync.com.au',
  ogImage: '/images/advisync-og-image.jpg',
  description: "Transform your business with AI voice agents and customer management automation. Melbourne's trusted partner for tradies (electricians, plumbers, builders) and healthcare providers (chiropractors, physios, dentists).",
  keywords: [
    'AI voice agents Melbourne',
    'customer management automation',
    'tradies automation electricians plumbers',
    'healthcare automation chiropractors physios',
    'business automation Melbourne',
    'AI receptionist Australia',
    'lead capture automation',
    'customer follow-up systems',
    'Melbourne AI solutions',
    'voice automation tradies',
    'healthcare practice automation',
    'service business automation',
    'Melbourne automation experts',
    'AI solutions provider'
  ],
  authors: [
    {
      name: 'Advisync AI Solutions',
      url: 'https://advisync.com.au',
    },
  ],
  creator: 'Advisync AI Solutions',
  themeColor: '#6366F1',
  icons: {
    icon: [
      { url: '/favicon/favicon.ico' },
      { url: '/favicon/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicon/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
    ],
    apple: [
      { url: '/favicon/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    other: [
      { rel: 'mask-icon', url: '/favicon/safari-pinned-tab.svg', color: '#6366F1' },
      { rel: 'manifest', url: '/site.webmanifest' }
    ],
  },
};

export function constructMetadata({
  title = siteConfig.name,
  description = siteConfig.description,
  keywords = siteConfig.keywords,
  image = siteConfig.ogImage,
  icons = siteConfig.icons,
  noIndex = false,
}: {
  title?: string
  description?: string
  keywords?: string[]
  image?: string
  icons?: typeof siteConfig.icons
  noIndex?: boolean
} = {}): Metadata {
  return {
    metadataBase: new URL(siteConfig.url),
    title,
    description,
    keywords,
    authors: siteConfig.authors,
    creator: siteConfig.creator,
    themeColor: siteConfig.themeColor,
    icons,
    manifest: '/site.webmanifest',
    openGraph: {
      type: 'website',
      locale: 'en_AU',
      url: siteConfig.url,
      title,
      description,
      siteName: siteConfig.name,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: title,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: [image],
      creator: '@advisync',
    },
    ...(noIndex && {
      robots: {
        index: false,
        follow: false,
      },
    }),
  };
}