import { NextResponse } from 'next/server';
import { sendSubscriptionToN8N } from '@/lib/n8n';
import fs from 'fs';
import path from 'path';

// Define a type for the global subscribers
declare global {
  var subscribers: Array<{ email: string; subscribedAt: string }>;
}

// Simple database to store subscribers
const DB_PATH = path.join(process.cwd(), 'subscribers.json');

// Initialize subscribers file if it doesn't exist
function initSubscribersFile() {
  try {
    if (!fs.existsSync(DB_PATH)) {
      fs.writeFileSync(DB_PATH, JSON.stringify({ subscribers: [] }), 'utf8');
    }
  } catch (error) {
    console.error('Error initializing subscribers file:', error);
    // Create a fallback in-memory storage if file system is not accessible
    global.subscribers = global.subscribers || [];
  }
}

// Save a new subscriber
function saveSubscriber(email: string) {
  try {
    initSubscribersFile();
    
    // Try to save to file first
    try {
      const data = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
      data.subscribers.push({
        email,
        subscribedAt: new Date().toISOString()
      });
      
      fs.writeFileSync(DB_PATH, JSON.stringify(data, null, 2), 'utf8');
    } catch (fileError) {
      console.error('Error saving to file, using memory storage:', fileError);
      // Fallback to in-memory storage
      global.subscribers = global.subscribers || [];
      global.subscribers.push({
        email,
        subscribedAt: new Date().toISOString()
      });
    }
    
    return true;
  } catch (error) {
    console.error('Error saving subscriber:', error);
    return false;
  }
}

// Check if email already exists
function emailExists(email: string): boolean {
  try {
    initSubscribersFile();
    
    // Try to check file first
    try {
      const data = JSON.parse(fs.readFileSync(DB_PATH, 'utf8'));
      return data.subscribers.some((sub: { email: string }) => sub.email === email);
    } catch (fileError) {
      console.error('Error reading from file, using memory storage:', fileError);
      // Fallback to in-memory storage
      global.subscribers = global.subscribers || [];
      return global.subscribers.some((sub: { email: string }) => sub.email === email);
    }
  } catch (error) {
    console.error('Error checking if email exists:', error);
    return false;
  }
}

export async function POST(request: Request) {
  try {
    const { email } = await request.json();
    
    // Validate email
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { message: 'Please provide a valid email address' },
        { status: 400 }
      );
    }
    
    // Check if email already exists
    if (emailExists(email)) {
      return NextResponse.json(
        { message: 'You are already subscribed to our newsletter!' },
        { status: 200 }
      );
    }
    
    // Save subscriber
    const saved = saveSubscriber(email);
    if (!saved) {
      return NextResponse.json(
        { message: 'An error occurred while saving your subscription' },
        { status: 500 }
      );
    }
    
    // Send data to N8N workflow
    const n8nResult = await sendSubscriptionToN8N({ email });

    if (!n8nResult.success) {
      console.error('N8N webhook failed:', n8nResult.error);
      // Continue with success response - don't block user experience
    }
    
    return NextResponse.json(
      { message: 'Thank you for subscribing! You will now receive our latest updates and insights.' },
      { status: 200 }
    );
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    return NextResponse.json(
      { message: 'An error occurred while processing your request' },
      { status: 500 }
    );
  }
} 