import React from 'react';
import { cn } from '@/lib/utils';

interface PageHeaderProps {
  title: string;
  description?: string;
  className?: string;
  children?: React.ReactNode;
}

export const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  className,
  children,
}) => {
  return (
    <div
      className={cn(
        'relative w-full py-10 md:py-16 lg:py-20 px-4 md:px-6 bg-background',
        className
      )}
    >
      <div className="container mx-auto">
        <div className="text-center max-w-3xl mx-auto">
          <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-4 tracking-tight">
            {title}
          </h1>
          {description && (
            <p className="text-base md:text-lg text-muted-foreground mb-6 leading-relaxed">
              {description}
            </p>
          )}
          {children}
        </div>
      </div>
    </div>
  );
};

export default PageHeader; 