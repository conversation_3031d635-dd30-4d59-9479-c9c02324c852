'use client';

import { useEffect, useRef } from 'react';
import { motion, useInView } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
  Workflow,
  Zap,
  ArrowRight,
  Database,
  RefreshCw,
  Bot,
  Settings,
  LineChart,
  Mail,
  MessageSquare,
  Calendar,
  Users
} from 'lucide-react';
import { Container } from '@/components/common/container';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const automationFeatures = [
  {
    title: "Business Process Automation",
    description: "Eliminate repetitive tasks and streamline your operations with custom workflow automation that connects all your business systems together.",
    icon: Workflow,
    benefits: [
      "Automate invoice generation and sending",
      "Sync customer data across all platforms",
      "Trigger follow-up actions automatically",
      "Reduce manual data entry by 90%"
    ]
  },
  {
    title: "CRM & Lead Management",
    description: "Automatically capture, qualify, and nurture leads from first contact to closed sale. Never lose track of a potential customer again.",
    icon: Bo<PERSON>,
    benefits: [
      "Auto-populate CRM from voice calls",
      "Score and route leads instantly",
      "Schedule automatic follow-ups",
      "Track customer journey end-to-end"
    ]
  },
  {
    title: "Multi-Channel Communication",
    description: "Keep customers engaged with automated SMS, email, and voice communications that feel personal and timely across their entire journey.",
    icon: MessageSquare,
    benefits: [
      "Send appointment reminders automatically",
      "Follow up on quotes and proposals",
      "Deliver service updates via SMS/email",
      "Re-engage dormant customers"
    ]
  },
  {
    title: "Smart Data Processing",
    description: "Transform raw customer interactions into actionable business insights with automated data collection, processing, and reporting.",
    icon: Database,
    benefits: [
      "Generate reports automatically",
      "Process forms and documents",
      "Track performance metrics",
      "Export data to any system"
    ]
  }
];

const workflowSteps = [
  {
    title: "Voice Interaction",
    icon: Bot,
    description: "AI voice agent interacts with customers and collects information"
  },
  {
    title: "Data Processing",
    icon: Database,
    description: "Information is processed and routed through intelligent automation workflows"
  },
  {
    title: "Business Logic",
    icon: RefreshCw,
    description: "Custom business rules determine next actions and responses"
  },
  {
    title: "Automated Actions",
    icon: Zap,
    description: "System triggers appropriate actions and follow-ups automatically"
  }
];

export function AutomationShowcase() {
  const workflowRef = useRef<HTMLDivElement>(null);
  const isInView = useInView(workflowRef, { once: true });

  useEffect(() => {
    if (!workflowRef.current) return;

    const workflow = workflowRef.current;

    gsap.fromTo(
      workflow.querySelectorAll('.workflow-step'),
      {
        opacity: 0,
        y: 20,
      },
      {
        opacity: 1,
        y: 0,
        duration: 0.5,
        stagger: 0.2,
        scrollTrigger: {
          trigger: workflow,
          start: 'top center+=100',
          toggleActions: 'play none none reverse',
        },
      }
    );
  }, []);

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-primary/5" />
        <div className="absolute inset-0 bg-grid opacity-10" />
      </div>

      <Container className="relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 tracking-tight leading-tight"
          >
            Business Automation That Actually Works
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-white/70 max-w-3xl mx-auto leading-relaxed text-lg"
          >
            Stop wasting time on repetitive tasks. Our automation solutions connect your voice agents with your business systems,
            creating seamless workflows that run your business while you focus on what matters most.
          </motion.p>
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {automationFeatures.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="group relative"
            >
              <div className="bg-background/50 border border-border/50 rounded-2xl p-8 transition-colors hover:border-primary/50">
                <div className="mb-6 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-accent-secondary group-hover:text-bg-primary transition-colors">
                  <feature.icon size={24} />
                </div>

                <h3 className="text-xl font-semibold mb-4 group-hover:text-accent-secondary transition-colors">
                  {feature.title}
                </h3>

                <p className="text-foreground/60 mb-6">
                  {feature.description}
                </p>

                <ul className="space-y-3">
                  {feature.benefits.map((benefit) => (
                    <li key={benefit} className="flex items-start space-x-2">
                      <ArrowRight className="w-5 h-5 text-accent-secondary mt-0.5" />
                      <span className="text-foreground/80">{benefit}</span>
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Workflow Visualization */}
        <div ref={workflowRef} className="relative">
          <div className="absolute inset-0 bg-primary/5 rounded-2xl" />

          <div className="relative bg-background/50 border border-border/50 rounded-2xl p-8">
            <h3 className="text-2xl font-semibold text-center mb-12 tracking-tight leading-tight">
              How Our Voice & Automation System Works
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {workflowSteps.map((step, index) => (
                <div
                  key={step.title}
                  className="workflow-step relative text-center"
                >
                  <div className="inline-flex h-16 w-16 items-center justify-center rounded-full bg-accent-secondary/10 text-accent-secondary mb-4 mx-auto">
                    <step.icon size={32} />
                  </div>

                  <h4 className="text-lg font-semibold mb-2">
                    {step.title}
                  </h4>

                  <p className="text-foreground/60">
                    {step.description}
                  </p>

                  {index < workflowSteps.length - 1 && (
                    <div className="hidden md:block absolute top-8 left-[60%] w-[80%] h-[2px] bg-accent-secondary/50" />
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </Container>
    </section>
  );
}