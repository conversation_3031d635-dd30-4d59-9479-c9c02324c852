import { Metadata } from 'next';
import { PricingSection } from '@/components/sections/pricing';
import { Container } from '@/components/common/container';
import { FAQ } from '@/components/sections/faq';
import { ConsultationCTA } from '@/components/sections/consultation-cta';

export const metadata: Metadata = {
  title: "AI Voice Agent Pricing | Transparent Plans | Advisync AI",
  description: "Simple, transparent pricing for AI voice agents and workflow automation. Choose from Starter ($1,999), Professional ($3,999), or Enterprise ($6,999) plans. All AUD pricing includes setup and support.",
  keywords: [
    'ai voice agent pricing australia',
    'virtual receptionist cost melbourne',
    'ai automation pricing plans',
    'business voice bot pricing',
    'australian ai voice agent cost',
    'voice automation pricing',
    'ai receptionist pricing australia',
    'voice agent subscription cost'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/pricing'
  },
  openGraph: {
    title: "AI Voice Agent Pricing | Transparent Plans | Advisync AI",
    description: "Simple, transparent pricing for AI voice agents and workflow automation. Plans starting from $1,999 AUD with full setup and support included.",
    url: 'https://advisync.com.au/pricing',
    siteName: 'Advisync AI',
    locale: 'en_AU',
    type: 'website',
    images: [
      {
        url: '/images/og/pricing-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI Pricing Plans - AI Voice Agents & Automation'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: "AI Voice Agent Pricing | Transparent Plans | Advisync AI",
    description: "Simple, transparent pricing for AI voice agents and workflow automation. Plans starting from $1,999 AUD.",
    images: ['/images/og/pricing-og.jpg']
  }
};

// Pricing FAQ items
const pricingFaqs = [
  {
    question: "What's included in the setup cost?",
    answer: "All our pricing includes complete setup, custom voice personality design, integration with your existing systems, comprehensive testing, team training, and initial optimization. There are no hidden setup fees."
  },
  {
    question: "Are there any ongoing monthly fees?",
    answer: "Our pricing is a one-time implementation cost that includes the specified support period. After the initial support period, you can choose to continue with optional monthly maintenance and optimization services starting from $299/month."
  },
  {
    question: "Can I upgrade my plan later?",
    answer: "Absolutely! You can upgrade your plan at any time. We'll credit your existing investment towards the higher tier and only charge the difference."
  },
  {
    question: "What if I'm not satisfied with the service?",
    answer: "We offer a 30-day satisfaction guarantee. If you're not completely satisfied with your AI voice agent within the first 30 days, we'll work with you to make it right or provide a full refund."
  },
  {
    question: "Do you offer payment plans?",
    answer: "Yes, we offer flexible payment plans for all our services. You can split the cost over 3, 6, or 12 months with no additional fees. Contact us to discuss payment options that work for your business."
  },
  {
    question: "What's the difference between the plans?",
    answer: "The main differences are in complexity and features. Starter is perfect for basic call handling, Professional adds advanced routing and analytics, while Enterprise includes multi-agent systems and custom integrations."
  },
  {
    question: "How long does implementation take?",
    answer: "Implementation typically takes 2-4 weeks depending on your plan and requirements. Starter plans can often be completed in 1-2 weeks, while Enterprise solutions may take 4-6 weeks for full deployment."
  },
  {
    question: "Do you provide training for our team?",
    answer: "Yes, comprehensive training is included in all plans. We provide hands-on training sessions, documentation, and ongoing support to ensure your team is comfortable using the new system."
  }
];

// JSON-LD structured data for pricing
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'AI Voice Agent Services',
  description: 'Professional AI voice agent and workflow automation services for Australian businesses',
  provider: {
    '@type': 'Organization',
    name: 'Advisync AI',
    url: 'https://advisync.com.au'
  },
  areaServed: {
    '@type': 'Country',
    name: 'Australia'
  },
  offers: [
    {
      '@type': 'Offer',
      name: 'Voice Starter',
      description: 'Perfect for small businesses getting started with AI voice agents',
      price: '1999',
      priceCurrency: 'AUD',
      availability: 'https://schema.org/InStock'
    },
    {
      '@type': 'Offer',
      name: 'Voice Professional',
      description: 'Comprehensive AI voice solution for growing businesses',
      price: '3999',
      priceCurrency: 'AUD',
      availability: 'https://schema.org/InStock'
    },
    {
      '@type': 'Offer',
      name: 'Voice Enterprise',
      description: 'Complete AI voice & automation ecosystem for established businesses',
      price: '6999',
      priceCurrency: 'AUD',
      availability: 'https://schema.org/InStock'
    }
  ]
};

export default function PricingPage() {
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      {/* Hero Section */}
      <section className="py-12 md:py-20">
        <Container>
          <div className="text-center max-w-4xl mx-auto px-4">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-text-heading">
              Simple, <span className="text-accent-secondary">Transparent Pricing</span>
            </h1>
            <p className="text-lg md:text-xl text-foreground/70 mb-8 max-w-3xl mx-auto">
              Choose the perfect AI voice agent solution for your Australian business. 
              All plans include complete setup, training, and ongoing support with no hidden fees.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-foreground/60">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>No setup fees</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>30-day guarantee</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>Australian support</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>Flexible payment plans</span>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Main Pricing Section */}
      <PricingSection />

      {/* Pricing FAQ */}
      <section className="py-20">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-accent-secondary">
                Pricing Questions & Answers
              </h2>
              <p className="text-foreground/60 max-w-2xl mx-auto">
                Everything you need to know about our pricing, plans, and what's included.
              </p>
            </div>

            <div className="space-y-4">
              {pricingFaqs.map((faq, index) => (
                <div
                  key={index}
                  className="border border-border/30 rounded-lg px-6 py-4 bg-background/50"
                >
                  <h3 className="text-lg font-medium mb-2 text-accent-secondary">
                    {faq.question}
                  </h3>
                  <p className="text-foreground/70">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <ConsultationCTA />
    </main>
  );
}
