import { Metadata } from 'next';
import { PricingSection } from '@/components/sections/pricing';
import { Container } from '@/components/common/container';
import { FAQ } from '@/components/sections/faq';
import { ConsultationCTA } from '@/components/sections/consultation-cta';

export const metadata: Metadata = {
  title: "Professional AI Business Assistant | $299/month | Advisync AI",
  description: "Save $57,000 annually vs hiring a receptionist. Complete Business Assistant for $299/month + $299 setup. 24/7 Australian AI receptionist with no contracts.",
  keywords: [
    'ai receptionist pricing australia',
    'virtual receptionist cost $299',
    'ai business assistant pricing',
    'professional reception service',
    'australian ai voice agent cost',
    'ai receptionist monthly cost',
    'business assistant pricing australia',
    'virtual receptionist subscription'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/pricing'
  },
  openGraph: {
    title: "Professional AI Business Assistant | $299/month | Advisync AI",
    description: "Save $57,000 annually vs hiring a receptionist. Complete Business Assistant for $299/month + $299 setup. 24/7 Australian AI receptionist.",
    url: 'https://advisync.com.au/pricing',
    siteName: 'Advisync AI',
    locale: 'en_AU',
    type: 'website',
    images: [
      {
        url: '/images/og/pricing-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI Business Assistant Pricing - $299/month'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: "Professional AI Business Assistant | $299/month | Advisync AI",
    description: "Save $57,000 annually vs hiring a receptionist. Complete Business Assistant for $299/month + $299 setup.",
    images: ['/images/og/pricing-og.jpg']
  }
};

// Warren Buffett Pricing FAQ items
const pricingFaqs = [
  {
    question: "Why no free trial?",
    answer: "We offer something better - a live demo where you can see your AI assistant working in real-time. This gives you a true understanding of the value without the complexity of a trial setup."
  },
  {
    question: "What if I exceed 200 calls per month?",
    answer: "We'll discuss upgrade options that fit your business growth. Our goal is to support your success, not penalize it with overage fees."
  },
  {
    question: "How quickly can I start?",
    answer: "Your AI business assistant will be handling calls within 24 hours of setup. We prioritize getting you operational fast so you don't miss any more opportunities."
  },
  {
    question: "What makes this better than other AI services?",
    answer: "Australian focus, business workflows included, and professional email confirmations. We're not just voice - we're a complete business assistant solution designed for Australian small businesses."
  },
  {
    question: "Why email confirmations instead of just voice?",
    answer: "Email confirmations provide complete professional records of every interaction. This gives you and your customers confidence and creates a paper trail for your business."
  },
  {
    question: "Can I cancel anytime?",
    answer: "Yes, no contracts. Cancel with 30 days notice. We believe in earning your business every month, not trapping you in long-term commitments."
  },
  {
    question: "What's included in the $299 setup?",
    answer: "Complete setup, Australian voice personality, integration with your calendar and CRM, testing, training, and optimization. Everything needed to get your AI assistant working perfectly for your business."
  },
  {
    question: "Do workflow add-ons require additional setup fees?",
    answer: "No, workflow add-ons are $49/month each with no additional setup costs. We can add them to your existing system seamlessly."
  }
];

// JSON-LD structured data for pricing
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Complete Business Assistant',
  description: 'Professional AI business assistant for Australian small businesses - Never miss a call again',
  provider: {
    '@type': 'Organization',
    name: 'Advisync AI',
    url: 'https://advisync.com.au'
  },
  areaServed: {
    '@type': 'Country',
    name: 'Australia'
  },
  offers: {
    '@type': 'Offer',
    name: 'Complete Business Assistant',
    description: 'Never miss a call again - Save $57,000 annually vs hiring a receptionist',
    price: '299',
    priceCurrency: 'AUD',
    priceSpecification: {
      '@type': 'UnitPriceSpecification',
      price: '299',
      priceCurrency: 'AUD',
      unitText: 'monthly'
    },
    availability: 'https://schema.org/InStock',
    validFrom: new Date().toISOString()
  }
};

export default function PricingPage() {
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      
      {/* Hero Section */}
      <section className="py-12 md:py-20">
        <Container>
          <div className="text-center max-w-4xl mx-auto px-4">
            <div className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-primary/20 border border-accent-secondary/30">
              <span className="text-sm font-medium text-accent-secondary">Warren Buffett Pricing Philosophy</span>
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-text-heading">
              Professional <span className="text-accent-secondary">AI Business Assistant</span>
            </h1>
            <p className="text-lg md:text-xl text-foreground/70 mb-8 max-w-3xl mx-auto">
              Save $57,000 annually vs hiring a receptionist. One perfect plan at a fair price
              that customers would gladly pay twice as much for.
            </p>
            <div className="flex flex-wrap justify-center gap-6 text-sm text-foreground/60 mb-8">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>No sick days or holidays</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Professional Australian voice</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>Complete interaction records</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span>No contracts - cancel anytime</span>
              </div>
            </div>
            <div className="bg-green-500/10 rounded-xl p-4 border border-green-500/20 max-w-md mx-auto">
              <p className="text-green-400 font-semibold text-lg">
                Essential business tool, not luxury
              </p>
            </div>
          </div>
        </Container>
      </section>

      {/* Main Pricing Section */}
      <PricingSection />

      {/* Pricing FAQ */}
      <section className="py-20">
        <Container>
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-accent-secondary">
                Pricing Questions & Answers
              </h2>
              <p className="text-foreground/60 max-w-2xl mx-auto">
                Everything you need to know about our pricing, plans, and what's included.
              </p>
            </div>

            <div className="space-y-4">
              {pricingFaqs.map((faq, index) => (
                <div
                  key={index}
                  className="border border-border/30 rounded-lg px-6 py-4 bg-background/50"
                >
                  <h3 className="text-lg font-medium mb-2 text-accent-secondary">
                    {faq.question}
                  </h3>
                  <p className="text-foreground/70">
                    {faq.answer}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <ConsultationCTA />
    </main>
  );
}
