'use client';

import { Dialog, DialogContent } from '@/components/common/dialog';
import { ArrowR<PERSON>, Check } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/common/button';

interface ServiceModalProps {
  isOpen: boolean;
  onClose: () => void;
  service: {
    title: string;
    description: string;
    features?: string[];
    benefits?: string[];
    bookingUrl?: string;
  };
}

export function ServiceModal({ isOpen, onClose, service }: ServiceModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] p-4 sm:p-6">
        <div className="space-y-6 sm:space-y-8">
          {/* Header */}
          <div>
            <h2 className="text-xl sm:text-2xl font-bold mb-2">{service.title}</h2>
            <p className="text-sm sm:text-base text-foreground/60">{service.description}</p>
          </div>

          {/* Features */}
          {service.features && service.features.length > 0 && (
            <div>
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Key Features</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-3">
                {service.features.map((feature) => (
                  <div 
                    key={feature}
                    className="flex items-start gap-2"
                  >
                    <Check className="w-4 h-4 sm:w-5 sm:h-5 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Benefits */}
          {service.benefits && service.benefits.length > 0 && (
            <div>
              <h3 className="text-base sm:text-lg font-semibold mb-3 sm:mb-4">Benefits</h3>
              <div className="grid grid-cols-1 gap-2 sm:gap-3">
                {service.benefits.map((benefit) => (
                  <div 
                    key={benefit}
                    className="flex items-start gap-2"
                  >
                    <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 text-primary mt-0.5 flex-shrink-0" />
                    <span className="text-sm sm:text-base">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* CTA */}
          {service.bookingUrl && (
            <div className="pt-2 sm:pt-4">
              <Button 
                asChild
                size="responsive"
                fullWidth
                className="gap-2"
              >
                <Link
                  href={service.bookingUrl}
                  onClick={onClose}
                >
                  <span>Free Consultation</span>
                  <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                </Link>
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 