"use client";

import Image from 'next/image';
import { useState } from 'react';
import { cn } from "@/lib/utils";

interface OptimizedImageProps {
  src: string;
  alt: string;
  width: number;
  height: number;
  priority?: boolean;
  className?: string;
  sizes?: string;
  quality?: number;
  fill?: boolean;
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  priority = false,
  className = '',
  sizes,
  quality = 85,
  fill = false,
  placeholder = 'empty',
  blurDataURL
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);

  // Generate responsive sizes if not provided
  const responsiveSizes = sizes || (
    priority
      ? "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
      : "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 25vw"
  );

  // Ensure image path is correct (handle .jpg vs .webp cases)
  let imageSrc = src;
  if (src.endsWith('.jpg') && !src.includes('_')) {
    // Try using the webp version if it's a simple .jpg path
    imageSrc = src.replace('.jpg', '.webp');
  }

  // Generate low-quality placeholder if not provided
  const defaultBlurDataURL = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q==";

  if (hasError) {
    return (
      <div
        className={cn(
          "relative flex items-center justify-center bg-secondary/5",
          "border border-primary/10 rounded-lg",
          className
        )}
        style={fill ? undefined : { width, height }}
      >
        <span className="text-sm text-foreground/60">Image not available</span>
      </div>
    );
  }

  const imageProps = {
    src: imageSrc,
    alt,
    priority,
    quality,
    sizes: responsiveSizes,
    className: cn(
      "duration-700 ease-in-out",
      isLoading ? "scale-110 blur-2xl grayscale" : "scale-100 blur-0 grayscale-0"
    ),
    onLoad: () => setIsLoading(false),
    onError: () => setHasError(true),
    placeholder,
    blurDataURL: blurDataURL || defaultBlurDataURL,
    ...(fill ? { fill: true } : { width, height })
  };

  return (
    <div className={cn("relative overflow-hidden rounded-lg", className)}>
      <Image {...imageProps} />
      {isLoading && (
        <div className="absolute inset-0 bg-secondary/5 animate-pulse" />
      )}
    </div>
  );
}