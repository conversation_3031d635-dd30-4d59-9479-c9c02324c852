"use client"

import { Container } from '@/components/common/container';
import {
  Calendar,
  Stethoscope,
  Dumbbell,
  UtensilsCrossed,
  ArrowRight,
  MapPin,
  Building2,
  Users,
  BarChart3,
  TrendingUp,
  Clock,
  LucideIcon,
  Target,
  Zap,
  Wrench,
  Heart
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { Dialog, DialogContent } from '@/components/common/dialog';

interface CaseStudyMetric {
  label: string;
  value: string;
}

interface CaseStudyDetails {
  challenge: string;
  solution: string;
  metrics: CaseStudyMetric[];
}

interface CaseStudy {
  title: string;
  description: string;
  icon: LucideIcon;
  location: string;
  tags: string[];
  details: CaseStudyDetails;
}

const caseStudies: CaseStudy[] = [
  {
    title: "Electrician Emergency Calls",
    description: "24/7 AI voice agent handling after-hours emergency calls and job bookings",
    icon: Zap,
    location: "Frankston",
    tags: ["AI Voice Agent", "Emergency Calls", "Job Booking"],
    details: {
      challenge: "Missing emergency calls after hours meant losing high-value jobs to competitors and frustrated customers calling multiple electricians.",
      solution: "Deployed AI voice agent with Australian accent to answer all calls, assess urgency, and book emergency or scheduled jobs instantly.",
      metrics: [
        { label: "Emergency Calls Captured", value: "100%" },
        { label: "After-Hours Revenue", value: "+85%" },
        { label: "Customer Satisfaction", value: "96%" }
      ]
    }
  },
  {
    title: "Physiotherapy Clinic Bookings",
    description: "AI receptionist managing appointments and patient communication",
    icon: Stethoscope,
    location: "Box Hill",
    tags: ["Healthcare", "AI Receptionist", "Appointment Booking"],
    details: {
      challenge: "Receptionist couldn't handle peak booking times, leading to missed calls and frustrated patients seeking treatment elsewhere.",
      solution: "Implemented AI receptionist to handle appointment bookings, cancellations, and basic patient inquiries with seamless calendar integration.",
      metrics: [
        { label: "Missed Calls Eliminated", value: "95%" },
        { label: "Booking Efficiency", value: "+60%" },
        { label: "New Patient Bookings", value: "+40%" }
      ]
    }
  },
  {
    title: "Plumbing Quote Follow-ups",
    description: "Automated workflow for quote tracking and customer follow-ups",
    icon: Wrench,
    location: "Dandenong",
    tags: ["Workflow Automation", "Quote Management", "Follow-ups"],
    details: {
      challenge: "Losing potential jobs because quotes weren't followed up consistently, and customers chose competitors who stayed in touch.",
      solution: "Created automated quote follow-up system with SMS reminders, email sequences, and job booking integration.",
      metrics: [
        { label: "Quote Conversion Rate", value: "+55%" },
        { label: "Follow-up Consistency", value: "100%" },
        { label: "Monthly Revenue", value: "+75%" }
      ]
    }
  },
  {
    title: "NDIS Support Coordination",
    description: "Automated participant communication and plan management system",
    icon: Users,
    location: "Geelong",
    tags: ["NDIS", "Participant Management", "Compliance"],
    details: {
      challenge: "Manual tracking of participant plans and communications was time-consuming and risked compliance issues with NDIS reporting.",
      solution: "Implemented automated participant communication system with plan tracking, appointment reminders, and compliance reporting.",
      metrics: [
        { label: "Admin Time Saved", value: "80%" },
        { label: "Compliance Accuracy", value: "100%" },
        { label: "Participant Satisfaction", value: "94%" }
      ]
    }
  },
  {
    title: "Chiropractic Treatment Plans",
    description: "Automated patient recall and treatment plan management",
    icon: Heart,
    location: "Ballarat",
    tags: ["Healthcare", "Treatment Plans", "Patient Recalls"],
    details: {
      challenge: "Patients weren't returning for follow-up treatments, impacting their recovery and the clinic's recurring revenue.",
      solution: "Built automated treatment plan reminders, progress check-ins, and recall system for ongoing patient care.",
      metrics: [
        { label: "Patient Return Rate", value: "+65%" },
        { label: "Treatment Completion", value: "+45%" },
        { label: "Recurring Revenue", value: "+90%" }
      ]
    }
  }
];

export function CaseStudies() {
  const [selectedStudy, setSelectedStudy] = useState<CaseStudy | null>(null);

  return (
    <section className="py-20">
      <Container>
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-accent-secondary/10 rounded-full px-4 py-2 mb-6 border border-accent-secondary/20">
            <span className="text-accent-secondary text-sm">⭐</span>
            <span className="text-accent-secondary text-sm font-medium">Trusted by 50+ Australian Businesses</span>
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-foreground leading-tight">
            Real Results for Real Businesses
          </h2>

          <p className="text-foreground/70 max-w-4xl mx-auto text-lg md:text-xl leading-relaxed mb-8">
            See how our AI voice agents and workflow automations are helping
            <span className="text-accent-secondary font-semibold"> tradies, healthcare providers, and NDIS services </span>
            capture more revenue, never miss calls, and save hours every week.
          </p>

          <div className="flex flex-wrap justify-center gap-6 text-sm text-foreground/60">
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>24/7 Call Coverage</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              <span>Instant Lead Capture</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
              <span>Automated Follow-ups</span>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {caseStudies.map((study) => (
            <div
              key={study.title}
              className="group cursor-pointer"
              onClick={() => setSelectedStudy(study)}
            >
              {/* Clean Card Design */}
              <div className="relative bg-surface-card/50 backdrop-blur-sm rounded-2xl p-6 border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:translate-y-[-4px] hover:shadow-lg">
                {/* Header with Icon */}
                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center shrink-0">
                    <study.icon className="w-6 h-6 text-primary" />
                  </div>
                  <ArrowRight className="w-5 h-5 text-primary/40 group-hover:text-primary group-hover:translate-x-1 transition-all duration-300" />
                </div>

                {/* Location */}
                <div className="flex items-center gap-2 text-sm text-primary/70 mb-3">
                  <MapPin className="w-4 h-4" />
                  <span>{study.location}</span>
                </div>

                {/* Title */}
                <h3 className="text-xl font-semibold mb-3 group-hover:text-primary transition-colors leading-tight">
                  {study.title}
                </h3>

                {/* Description */}
                <p className="text-foreground/70 leading-relaxed mb-4 text-sm">
                  {study.description}
                </p>

                {/* Quick Results Preview */}
                <div className="bg-accent-secondary/5 rounded-lg p-3 mb-4 border border-accent-secondary/10">
                  <div className="flex items-center justify-between">
                    <span className="text-xs text-foreground/60">Key Result:</span>
                    <span className="text-sm font-semibold text-accent-secondary">
                      {study.details.metrics[0]?.value} {study.details.metrics[0]?.label}
                    </span>
                  </div>
                </div>

                {/* Tags */}
                <div className="flex flex-wrap gap-2">
                  {study.tags.slice(0, 2).map((tag) => (
                    <span
                      key={tag}
                      className="px-2 py-1 bg-primary/5 rounded-lg text-xs text-primary/80 border border-primary/10"
                    >
                      {tag}
                    </span>
                  ))}
                  {study.tags.length > 2 && (
                    <span className="px-2 py-1 text-xs text-primary/60">
                      +{study.tags.length - 2} more
                    </span>
                  )}
                </div>

                {/* Hover Effect Indicator */}
                <div className="absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="text-xs text-primary/60">Click to view details</div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-12">
          <Link 
            href="/case-studies"
            className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors group"
          >
            <span>View All Case Studies</span>
            <ArrowRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>

        {/* Case Study Details Modal */}
        <Dialog 
          open={selectedStudy !== null} 
          onOpenChange={(open) => !open && setSelectedStudy(null)}
        >
          <DialogContent className="max-w-4xl p-0 overflow-hidden bg-background border border-primary/20">
            {selectedStudy && (
              <div className="relative">
                {/* Content */}
                <div className="relative p-6 md:p-8 space-y-8">
                  <div className="flex items-start justify-between gap-4">
                    <div>
                      <div className="flex items-center gap-2 text-sm text-primary/70 mb-3">
                        <MapPin className="w-4 h-4" />
                        <span>{selectedStudy.location}</span>
                      </div>
                      <h3 className="text-2xl md:text-3xl font-bold text-foreground mb-4">
                        {selectedStudy.title}
                      </h3>
                      <p className="text-foreground/60 text-lg leading-relaxed">
                        {selectedStudy.description}
                      </p>
                    </div>
                    <div className="w-16 h-16 rounded-2xl bg-primary/10 flex items-center justify-center shrink-0">
                      <selectedStudy.icon className="w-8 h-8 text-primary" />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-8 pt-6 border-t border-border/20">
                    {/* Challenge */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 text-lg font-semibold">
                        <div className="w-10 h-10 rounded-xl bg-red-50 flex items-center justify-center border border-red-100">
                          <Target className="w-5 h-5 text-red-600" />
                        </div>
                        <h4 className="text-foreground">The Challenge</h4>
                      </div>
                      <p className="text-foreground/70 leading-relaxed text-base">
                        {selectedStudy.details.challenge}
                      </p>
                    </div>

                    {/* Solution */}
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 text-lg font-semibold">
                        <div className="w-10 h-10 rounded-xl bg-primary/10 flex items-center justify-center border border-primary/20">
                          <Zap className="w-5 h-5 text-primary" />
                        </div>
                        <h4 className="text-foreground">Our Solution</h4>
                      </div>
                      <p className="text-foreground/70 leading-relaxed text-base">
                        {selectedStudy.details.solution}
                      </p>
                    </div>
                  </div>

                  {/* Metrics */}
                  <div className="pt-6 border-t border-border/20">
                    <div className="flex items-center gap-3 text-lg font-semibold mb-6">
                      <div className="w-10 h-10 rounded-xl bg-green-50 flex items-center justify-center border border-green-100">
                        <BarChart3 className="w-5 h-5 text-green-600" />
                      </div>
                      <h4 className="text-foreground">Key Results</h4>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {selectedStudy.details.metrics.map((metric) => (
                        <div
                          key={metric.label}
                          className="text-center p-6 rounded-xl bg-surface-card/30 border border-primary/10 hover:border-primary/20 transition-all duration-300"
                        >
                          <div className="text-3xl font-bold text-accent-secondary mb-2">
                            {metric.value}
                          </div>
                          <div className="text-sm text-foreground/60 font-medium">
                            {metric.label}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 pt-6 border-t border-border/20">
                    {selectedStudy.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-2 bg-primary/5 rounded-lg text-sm text-primary border border-primary/10"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>
      </Container>
    </section>
  );
}
