# Advisync AI Solutions

This is the repository for the Advisync AI Solutions website, built with Next.js, TypeScript, and Tailwind CSS.

## Environment Variables

The application requires several environment variables to be set up. You can find examples in the `.env.example` file.

### Required Environment Variables for Production (Vercel)

The following environment variables are required for production deployment:

- `N8N_WEBHOOK_URL`: Required for N8N workflow integration (email handling)
- `VAPI_API_KEY`: Required for Vapi voice agent integration

### Optional Environment Variables (But Recommended)

These variables are optional in production but recommended for full functionality:

- `NEXT_PUBLIC_API_URL`: Base URL for API calls (e.g., `https://your-domain.com/api`)
- `WEBHOOK_SECRET_KEY`: Secret key for webhook verification
- `NOTIFICATION_EMAIL`: Email address for receiving notifications (defaults to '<EMAIL>')

## Setup Instructions for Vercel Deployment

1. Fork or clone this repository
2. Create a new project in Vercel
3. Link your repository to the Vercel project
4. Add the required environment variables in the Vercel project settings:
   - Go to Project Settings > Environment Variables
   - Add each variable with its corresponding value
5. Deploy the project

## Local Development

1. Clone this repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Create a `.env.local` file with the required environment variables (see `.env.example`)
4. Run the development server:
   ```bash
   npm run dev
   ```
5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Contact

For any inquiries, please contact <NAME_EMAIL>