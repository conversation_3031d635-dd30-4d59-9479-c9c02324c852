'use client';

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X, ChevronRight, Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';
import { navigationItems } from '@/config/navigation';
import { Logo } from '@/components/common/logo';
import { cn } from '@/lib/utils';

export function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [viewportWidth, setViewportWidth] = useState(0);
  const pathname = usePathname();
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  // Handle outside clicks for mobile menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node) && isOpen) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isOpen]);

  // Handle scroll events
  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 20);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Handle viewport width changes
  useEffect(() => {
    const handleResize = () => {
      setViewportWidth(window.innerWidth);
      // Close mobile menu when resizing to desktop - updated breakpoint to 965px
      if (window.innerWidth >= 965 && isOpen) {
        setIsOpen(false);
      }
    };

    // Set initial width
    handleResize();
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [isOpen]);

  // Close mobile menu when route changes
  useEffect(() => {
    setIsOpen(false);
  }, [pathname]);

  const toggleMenu = () => setIsOpen(!isOpen);

  // Determine logo size based on viewport
  const getLogoSize = () => {
    if (viewportWidth < 375) return { width: 120, height: 39 };
    if (viewportWidth < 640) return { width: 150, height: 49 };
    return { width: 180, height: 59 };
  };

  // Check if we should show mobile menu based on viewport width
  const isMobileView = viewportWidth < 965;

  // Add social links data
  const socialLinks = [
    { 
      icon: Facebook, 
      href: "https://www.facebook.com/profile.php?id=61573127892569", 
      label: "Facebook",
      color: "hover:bg-blue-600"
    },
    { 
      icon: Twitter, 
      href: "https://x.com/Advisync_AI_Sol", 
      label: "X",
      color: "hover:bg-black"
    },
    { 
      icon: Instagram, 
      href: "https://www.instagram.com/advisync_solutions_/", 
      label: "Instagram",
      color: "hover:bg-gradient-to-r hover:from-purple-500 hover:via-pink-500 hover:to-orange-500"
    },
    { 
      icon: Linkedin, 
      href: "https://www.linkedin.com/company/advisync-solutions", 
      label: "LinkedIn",
      color: "hover:bg-blue-700"
    }
  ];

  return (
    <header className="fixed top-0 w-full z-50 px-4 sm:px-6 lg:px-8 py-2 sm:py-3 md:py-4">
      <motion.nav 
        className={cn(
          "mx-auto max-w-7xl rounded-xl sm:rounded-2xl border border-white/10",
          "bg-background/60 backdrop-blur-xl",
          "transition-all duration-300 ease-in-out",
          scrolled 
            ? "shadow-lg shadow-primary/10 border-primary/20" 
            : "hover:border-primary/20 hover:shadow-lg hover:shadow-primary/5"
        )}
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="mx-auto h-14 sm:h-16">
          <div className="flex items-center justify-between h-full px-4 sm:px-6 lg:px-8">
            {/* Logo with glow effect */}
            <motion.div 
              className="flex-shrink-0 relative"
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 400, damping: 10 }}
            >
              <div className="absolute -inset-1 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <Logo 
                className="py-1 sm:py-2 relative z-10" 
                width={getLogoSize().width}
                height={getLogoSize().height}
              />
            </motion.div>

            {/* Desktop Navigation - Enhanced with animations and effects */}
            <div className={cn("items-center space-x-4 lg:space-x-8", isMobileView ? "hidden" : "flex")}>
              {navigationItems.map((item, index) => (
                <motion.div
                  key={item.href}
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="relative"
                >
                  <Link
                    href={item.href}
                    className={cn(
                      "text-sm font-medium transition-colors hover:text-primary relative group flex items-center px-2 py-1",
                      pathname === item.href
                        ? "text-primary"
                        : "text-foreground/70"
                    )}
                  >
                    <span className="relative z-10">
                      {item.title}
                      {pathname === item.href && (
                        <motion.span 
                          layoutId="navbar-underline"
                          className="absolute -bottom-1 left-0 w-full h-0.5 bg-gradient-to-r from-primary to-secondary"
                        />
                      )}
                    </span>
                    <span className="absolute inset-0 rounded-full -z-10 opacity-0 group-hover:opacity-100 bg-primary/5 transition-opacity duration-300"></span>
                  </Link>
                </motion.div>
              ))}
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 0.5 }}
                whileHover={{ scale: 1.05 }}
                className="relative"
              >
                <Link
                  href="/consultation"
                  className="bg-accent-secondary hover:bg-accent-secondary/90 text-bg-primary hover:text-bg-primary px-4 sm:px-6 py-2 sm:py-2.5 rounded-full text-sm font-medium transition-all duration-300 hover:shadow-lg hover:shadow-glow-gold flex items-center gap-2 group"
                >
                  <span>Get Started</span>
                  <ChevronRight className="w-4 h-4 transform group-hover:translate-x-1 transition-transform" />
                </Link>
              </motion.div>
            </div>

            {/* Mobile Menu Button - Enhanced with animations */}
            <motion.button
              onClick={toggleMenu}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              aria-label={isOpen ? "Close menu" : "Open menu"}
              className={cn("p-2 text-foreground/60 hover:text-primary transition-colors rounded-full hover:bg-primary/10 relative", isMobileView ? "block" : "hidden")}
            >
              {isOpen ? <X size={22} /> : <Menu size={22} />}
            </motion.button>
          </div>
        </div>
      </motion.nav>

      {/* Mobile Navigation - Enhanced with better animations and styling */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={mobileMenuRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className={cn(isMobileView ? "block" : "hidden", "mt-2 mx-auto max-w-7xl")}
          >
            <div className="bg-background/90 backdrop-blur-xl border border-primary/20 rounded-xl sm:rounded-2xl shadow-lg shadow-primary/10 overflow-hidden">
              <div className="mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6 space-y-1 sm:space-y-2">
                {navigationItems.map((item, index) => (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <Link
                      href={item.href}
                      onClick={() => setIsOpen(false)}
                      className={cn(
                        "flex items-center justify-between text-sm font-medium transition-colors hover:text-primary p-3 rounded-xl",
                        "touch-manipulation active:bg-primary/20 active:scale-[0.98] transition-transform",
                        pathname === item.href
                          ? "text-primary bg-primary/10 border border-primary/20"
                          : "text-foreground/70 hover:bg-primary/5"
                      )}
                    >
                      <span>{item.title}</span>
                      {pathname === item.href && (
                        <motion.div 
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ delay: 0.2 }}
                        >
                          <ChevronRight className="w-4 h-4 text-primary" />
                        </motion.div>
                      )}
                    </Link>
                  </motion.div>
                ))}
                
                {/* Social Media Links */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.3 }}
                  className="mt-6 pb-2 border-b border-white/10"
                >
                  <div className="flex flex-wrap justify-center gap-3 py-2">
                    <p className="w-full text-center text-sm text-foreground/60 mb-2">Follow Us</p>
                    {socialLinks.map((social, index) => (
                      <motion.a
                        key={social.label}
                        href={social.href}
                        target="_blank"
                        rel="noopener noreferrer"
                        onClick={(e) => e.stopPropagation()}
                        aria-label={social.label}
                        className={`w-10 h-10 rounded-full bg-secondary/10 flex items-center justify-center ${social.color} transition-colors group`}
                        whileTap={{ scale: 0.95 }}
                        initial={{ opacity: 0, y: 5 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.1 * index + 0.3 }}
                      >
                        <social.icon 
                          size={18} 
                          className="text-foreground/80 group-hover:text-white transition-colors" 
                        />
                      </motion.a>
                    ))}
                  </div>
                </motion.div>
                
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="pt-2"
                >
                  <Link
                    href="/consultation"
                    onClick={() => setIsOpen(false)}
                    className="block bg-accent-secondary hover:bg-accent-secondary/90 text-bg-primary hover:text-bg-primary px-4 py-3 rounded-xl text-sm font-medium transition-all duration-300 text-center mt-4 hover:shadow-lg hover:shadow-glow-gold flex items-center justify-center gap-2 active:scale-[0.98] touch-manipulation"
                  >
                    <span>Get Started</span>
                    <ChevronRight className="w-4 h-4" />
                  </Link>
                </motion.div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
} 