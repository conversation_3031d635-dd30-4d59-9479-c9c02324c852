import { env } from './env';

interface ContactFormData {
  name: string;
  email: string;
  company: string;
  phone: string;
  teamSize?: string;
  contactPreference?: string;
  service: string;
  message: string;
}

interface NewsletterData {
  email: string;
}

interface ConsultationData {
  name: string;
  email: string;
  phone: string;
  company: string;
  teamSize?: string;
  service: string;
  serviceName?: string;
  serviceCategory?: string;
  time: string;
  message?: string;
  submitDate: string;
}

/**
 * Send data to N8N webhook
 */
async function sendToN8N(webhookUrl: string, data: any): Promise<{ success: boolean; data?: any; error?: string }> {
  try {
    const response = await fetch(webhookUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        timestamp: new Date().toISOString(),
        source: 'advisync-website'
      }),
    });

    if (!response.ok) {
      throw new Error(`N8N webhook failed: ${response.status} ${response.statusText}`);
    }

    return { success: true, data: await response.json() };
  } catch (error) {
    console.error('N8N webhook error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}

/**
 * Send contact form data to N8N
 */
export async function sendContactFormToN8N(data: ContactFormData) {
  const webhookUrl = env.N8N_CONTACT_FORM_WEBHOOK;
  
  if (!webhookUrl) {
    console.warn('N8N_CONTACT_FORM_WEBHOOK not configured. Form data not sent to N8N.');
    return { success: true }; // Return success to not block form submission
  }

  return sendToN8N(webhookUrl, {
    type: 'contact_form',
    ...data
  });
}

/**
 * Send newsletter subscription to N8N
 */
export async function sendNewsletterToN8N(data: NewsletterData) {
  const webhookUrl = env.N8N_NEWSLETTER_WEBHOOK;
  
  if (!webhookUrl) {
    console.warn('N8N_NEWSLETTER_WEBHOOK not configured. Newsletter data not sent to N8N.');
    return { success: true }; // Return success to not block form submission
  }

  return sendToN8N(webhookUrl, {
    type: 'newsletter_subscription',
    ...data
  });
}

/**
 * Send consultation form data to N8N
 */
export async function sendConsultationToN8N(data: ConsultationData) {
  const webhookUrl = env.N8N_CONTACT_FORM_WEBHOOK; // Using same webhook as contact form
  
  if (!webhookUrl) {
    console.warn('N8N_CONTACT_FORM_WEBHOOK not configured. Consultation data not sent to N8N.');
    return { success: true }; // Return success to not block form submission
  }

  return sendToN8N(webhookUrl, {
    type: 'consultation_request',
    ...data
  });
}

/**
 * Send subscription data to N8N (for the subscribe route)
 */
export async function sendSubscriptionToN8N(data: { email: string }) {
  const webhookUrl = env.N8N_NEWSLETTER_WEBHOOK;
  
  if (!webhookUrl) {
    console.warn('N8N_NEWSLETTER_WEBHOOK not configured. Subscription data not sent to N8N.');
    return { success: true }; // Return success to not block form submission
  }

  return sendToN8N(webhookUrl, {
    type: 'newsletter_subscription',
    ...data
  });
}
