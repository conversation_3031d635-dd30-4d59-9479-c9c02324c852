import { MetadataRoute } from 'next';

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://advisync.com.au';
  
  // Define routes with their specific configurations
  const routes = [
    // Core Pages - High Priority
    {
      path: '',
      changeFrequency: 'daily' as const,
      priority: 1.0, // Homepage gets highest priority
    },
    {
      path: '/services',
      changeFrequency: 'weekly' as const,
      priority: 0.9, // Main service page
    },
    {
      path: '/pricing',
      changeFrequency: 'weekly' as const,
      priority: 0.9, // Important pricing page
    },
    {
      path: '/consultation',
      changeFrequency: 'weekly' as const,
      priority: 0.9, // Important conversion page
    },
    {
      path: '/about',
      changeFrequency: 'monthly' as const,
      priority: 0.8, // Company information
    },
    {
      path: '/contact',
      changeFrequency: 'monthly' as const,
      priority: 0.8, // Contact information
    },

    // Dynamic Content Pages - Medium-High Priority
    {
      path: '/blog',
      changeFrequency: 'daily' as const,
      priority: 0.8, // Frequently updated content
    },
    {
      path: '/case-studies',
      changeFrequency: 'weekly' as const,
      priority: 0.8, // Success stories
    },

    // Legal Pages - Lower Priority
    {
      path: '/privacy',
      changeFrequency: 'yearly' as const,
      priority: 0.3, // Legal content, less frequent updates
    },
    {
      path: '/terms',
      changeFrequency: 'yearly' as const,
      priority: 0.3, // Legal content
    },
    {
      path: '/cookies',
      changeFrequency: 'yearly' as const,
      priority: 0.3, // Legal content
    }
  ];

  return routes.map((route) => ({
    url: `${baseUrl}${route.path}`,
    lastModified: new Date().toISOString(),
    changeFrequency: route.changeFrequency,
    priority: route.priority,
  }));
} 