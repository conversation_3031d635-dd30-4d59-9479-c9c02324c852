'use client';

import { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import {
  Brain,
  MessageSquareCode,
  Zap,
  Bot,
  Network,
  LineChart,
  Fingerprint
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { cn } from '@/lib/utils';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

const solutions = [
  {
    title: "24/7 AI Voice Receptionist",
    description: "Transform every call into an opportunity. Our AI receptionists handle calls with authentic Australian accents, book appointments instantly, capture leads, and ensure no customer ever reaches voicemail again - even during your busiest days.",
    icon: MessageSquareCode,
    features: [
      "Natural Australian accent and local terminology",
      "Instant appointment booking with calendar sync",
      "Automatic lead capture and qualification",
      "Smart call routing for urgent matters",
      "Seamless integration with existing phone systems",
      "Custom scripts tailored to your business",
      "Real-time call transcripts and summaries"
    ],
    demo: {
      type: "chat",
      preview: "Natural voice conversations with customers",
    }
  },
  {
    title: "Intelligent Lead Management",
    description: "Convert more prospects into paying customers. Our AI conducts intelligent conversations, scores leads based on your criteria, and automatically routes high-value prospects to your sales team while you focus on service delivery.",
    icon: Brain,
    features: [
      "Custom qualification questionnaires",
      "AI-powered lead scoring algorithms",
      "Automated hot lead notifications",
      "CRM integration and data sync",
      "Follow-up scheduling and reminders",
      "Conversion tracking and analytics",
      "Priority routing for high-value leads"
    ],
    demo: {
      type: "graph",
      preview: "Intelligent lead scoring and routing",
    }
  },
  {
    title: "Automated Customer Support",
    description: "Deliver exceptional customer service around the clock. Handle common enquiries instantly, provide technical support, and escalate complex issues to your team - all while maintaining your brand's professional voice and values.",
    icon: Bot,
    features: [
      "Instant FAQ and enquiry resolution",
      "Technical troubleshooting assistance",
      "Smart escalation to human agents",
      "Complete conversation history tracking",
      "24/7 availability including holidays",
      "Multi-channel support (phone, chat, email)",
      "Customer satisfaction monitoring"
    ],
    demo: {
      type: "dashboard",
      preview: "Multi-channel customer support automation",
    }
  },
  {
    title: "Workflow Automation Hub",
    description: "Connect your voice agents to your entire business ecosystem. Automatically update CRMs, generate quotes, send follow-ups, and trigger workflows - eliminating manual data entry and ensuring nothing falls through the cracks.",
    icon: Zap,
    features: [
      "500+ business tool integrations",
      "Automatic CRM record updates",
      "Quote and invoice generation",
      "Email and SMS follow-up sequences",
      "Task creation and assignment",
      "Document generation and storage",
      "Custom workflow triggers and actions"
    ],
    demo: {
      type: "workflow",
      preview: "Seamless integration with your existing tools",
    }
  }
];

const features = [
  {
    icon: Bot,
    title: "Australian-Made AI",
    description: "Built for Australian businesses with local accents, terminology, and business practices"
  },
  {
    icon: Network,
    title: "Plug & Play Setup",
    description: "Works with your existing phone system - no complex installations or downtime required"
  },
  {
    icon: LineChart,
    title: "Real-Time Analytics",
    description: "Track call volumes, conversion rates, and revenue impact with detailed reporting dashboards"
  },
  {
    icon: Fingerprint,
    title: "Your Brand Voice",
    description: "Custom-trained AI that speaks and acts exactly like your best team member"
  }
];

function DemoCard({ solution, index }: { solution: typeof solutions[0], index: number }) {
  const cardRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  useEffect(() => {
    if (!cardRef.current) return;

    const card = cardRef.current;

    gsap.fromTo(card,
      {
        opacity: 0,
        y: 50
      },
      {
        opacity: 1,
        y: 0,
        duration: 1,
        ease: "power3.out",
        scrollTrigger: {
          trigger: card,
          start: "top bottom-=100",
          toggleActions: "play none none reverse"
        }
      }
    );
  }, []);

  return (
    <div
      ref={cardRef}
      className={cn(
        "relative group cursor-pointer",
        index % 2 === 0 ? "lg:translate-y-8" : ""
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="bg-background/50 border border-border/50 rounded-2xl p-6 transition-all duration-300 hover:border-primary/50 hover:translate-y-[-8px]">
        <div className="mb-4 inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-white transition-colors">
          <solution.icon size={24} />
        </div>

        <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
          {solution.title}
        </h3>

        <p className="text-foreground/60 mb-4">
          {solution.description}
        </p>

        {/* Features List */}
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-foreground/80 mb-2">What's Included:</h4>
          <ul className="space-y-1">
            {solution.features.map((feature, idx) => (
              <li key={idx} className="text-sm text-foreground/60 flex items-start gap-2">
                <span className="text-accent-secondary mt-1 font-bold">✓</span>
                {feature}
              </li>
            ))}
          </ul>
        </div>

        {/* Interactive Demo Preview */}
        <div className="relative h-32 bg-background/30 rounded-lg overflow-hidden">
          <div className={cn(
            "absolute inset-0 flex items-center justify-center transition-all duration-500",
            isHovered ? "opacity-0" : "opacity-100"
          )}>
            <p className="text-foreground/40 text-sm">
              Hover to preview {solution.demo.preview}
            </p>
          </div>

          <div className={cn(
            "absolute inset-0 bg-primary/5 transition-all duration-500",
            isHovered ? "opacity-100" : "opacity-0"
          )}>
            {/* Demo content based on type */}
            {solution.demo.type === "chat" && (
              <ChatDemo />
            )}
            {solution.demo.type === "graph" && (
              <GraphDemo />
            )}
            {solution.demo.type === "dashboard" && (
              <DashboardDemo />
            )}
            {solution.demo.type === "workflow" && (
              <WorkflowDemo />
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

// Demo Components
function ChatDemo() {
  return (
    <div className="p-4 h-full flex flex-col">
      <div className="flex-1 space-y-4">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-primary/10 rounded-lg p-3 max-w-[80%]"
        >
          How can I help you today?
        </motion.div>
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="bg-secondary/10 rounded-lg p-3 max-w-[80%] ml-auto"
        >
          I need help with automation.
        </motion.div>
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 1 }}
          className="bg-primary/10 rounded-lg p-3 max-w-[80%]"
        >
          I can help you streamline your workflow...
        </motion.div>
      </div>
    </div>
  );
}

function GraphDemo() {
  const canvasRef = useRef<HTMLCanvasElement>(null);

  useEffect(() => {
    if (!canvasRef.current) return;

    const ctx = canvasRef.current.getContext('2d');
    if (!ctx) return;

    const points = Array.from({ length: 10 }, (_, i) => ({
      x: i * 30 + 20,
      y: Math.sin(i * 0.5) * 30 + 50
    }));

    gsap.to(points, {
      duration: 2,
      y: '+=20',
      ease: "sine.inOut",
      stagger: 0.1,
      repeat: -1,
      yoyo: true,
      onUpdate: () => {
        if (!ctx || !canvasRef.current) return;

        ctx.clearRect(0, 0, canvasRef.current.width, canvasRef.current.height);
        ctx.beginPath();
        ctx.moveTo(points[0].x, points[0].y);

        points.forEach((point, i) => {
          if (i === 0) return;
          ctx.lineTo(point.x, point.y);
        });

        ctx.strokeStyle = '#6366F1';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
    });
  }, []);

  return (
    <div className="p-4 h-full flex items-center justify-center">
      <canvas
        ref={canvasRef}
        width={300}
        height={100}
        className="w-full h-full"
      />
    </div>
  );
}

function DashboardDemo() {
  return (
    <div className="p-4 h-full grid grid-cols-2 gap-4">
      {[1, 2, 3, 4].map((i) => (
        <motion.div
          key={i}
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, delay: i * 0.1 }}
          className="bg-primary/10 rounded-lg p-2"
        >
          <div className="h-2 w-16 bg-primary/20 rounded" />
          <div className="mt-2 h-8 bg-primary/5 rounded" />
        </motion.div>
      ))}
    </div>
  );
}

function WorkflowDemo() {
  return (
    <div className="p-4 h-full flex items-center justify-center">
      <div className="flex items-center space-x-4">
        {[1, 2, 3].map((i) => (
          <motion.div
            key={i}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: i * 0.2 }}
            className="flex items-center"
          >
            <div className="w-8 h-8 rounded-full bg-primary/20 flex items-center justify-center">
              {i}
            </div>
            {i < 3 && (
              <motion.div
                initial={{ width: 0 }}
                animate={{ width: 40 }}
                transition={{ duration: 0.5, delay: i * 0.2 + 0.2 }}
                className="h-0.5 bg-primary/20"
              />
            )}
          </motion.div>
        ))}
      </div>
    </div>
  );
}

export function AISolutions() {
  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-primary/5" />
        <div className="absolute top-0 left-0 w-full h-full bg-grid opacity-20" />
      </div>

      <Container className="relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-primary/20 border border-accent-secondary/30"
          >
            <Bot className="w-5 h-5 text-accent-secondary" />
            <span className="text-sm font-medium text-accent-secondary">AI-Powered Business Solutions</span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 tracking-tight leading-tight"
          >
            AI Voice & Automation Solutions
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-foreground/70 max-w-4xl mx-auto leading-relaxed text-lg mb-4"
          >
            Transform your Australian small business with cutting-edge AI voice agents and intelligent workflow automation.
            Designed specifically for tradies, clinics, cafés, and service providers who need to capture every opportunity while focusing on what they do best.
          </motion.p>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="text-primary font-semibold text-lg"
          >
            Never miss a call, lose a lead, or waste time on repetitive tasks again.
          </motion.p>
        </div>

        {/* Solutions Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
          {solutions.map((solution, index) => (
            <DemoCard key={solution.title} solution={solution} index={index} />
          ))}
        </div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className="text-center"
            >
              <div className="inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary mb-4">
                <feature.icon size={24} />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {feature.title}
              </h3>
              <p className="text-foreground/60">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>
      </Container>
    </section>
  );
}