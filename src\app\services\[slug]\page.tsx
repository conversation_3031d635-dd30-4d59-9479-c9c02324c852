import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { services } from '@/config/services';
import { Container } from '@/components/common/container';
import { PageHeader } from '@/components/common/page-header';
import {
  CheckCircle,
  ArrowRight,
  Users,
  Calendar,
  FileText,
  Cog,
  MessageSquare,
  BarChart2,
  Clock,
  ClipboardCheck,
  Target,
  Mail,
  Globe,
  Lightbulb
} from 'lucide-react';
import Link from 'next/link';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/common/accordion";

// Map of icon names to Lucide icon components
const iconMap: Record<string, any> = {
  Users,
  Calendar,
  FileText,
  Cog,
  MessageSquare,
  BarChart2,
  Clock,
  ClipboardCheck,
  Target,
  Mail,
  Globe,
  Lightbulb
};

// Generate metadata for each service page
export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const service = services.find(s => s.slug === params.slug);

  if (!service) {
    return {
      title: 'Service Not Found | Advisync Solutions',
      description: 'The requested service could not be found.',
    };
  }

  return {
    title: `${service.title} | Advisync AI Solutions Melbourne`,
    description: `${service.description} Expert ${service.title.toLowerCase()} services tailored for Melbourne businesses.`,
    keywords: [
      ...service.tags,
      'Melbourne business automation',
      'digital transformation',
      'business solutions',
      'automation services',
      `${service.title.toLowerCase()} Melbourne`,
      'business efficiency',
      'process optimization'
    ],
    alternates: {
      canonical: `https://advisync.com.au/services/${service.slug}`
    },
    openGraph: {
      title: `${service.title} | Advisync AI Solutions`,
      description: service.description,
      url: `https://advisync.com.au/services/${service.slug}`,
      type: 'website',
      siteName: 'Advisync AI Solutions',
      locale: 'en_AU',
      images: [
        {
          url: service.image || '/images/og/services-og.jpg',
          width: 1200,
          height: 630,
          alt: `${service.title} - Advisync AI Solutions`
        }
      ]
    }
  };
}

export default function ServicePage({ params }: { params: { slug: string } }) {
  // Special case for NDIS service which has its own dedicated page
  if (params.slug === 'ndis') {
    // This will be handled by the dedicated NDIS page
    return null;
  }

  const service = services.find(s => s.slug === params.slug);

  if (!service) {
    notFound();
  }

  // Generate JSON-LD structured data for this service
  const jsonLd = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.title,
    description: service.longDescription || service.description,
    provider: {
      '@type': 'Organization',
      name: 'Advisync Solutions',
      url: 'https://advisync.com.au',
      logo: {
        '@type': 'ImageObject',
        url: 'https://advisync.com.au/images/advisync-logo.png'
      }
    },
    areaServed: {
      '@type': 'City',
      name: 'Melbourne',
      '@id': 'https://www.wikidata.org/wiki/Q3141'
    },
    serviceType: service.title,
    offers: {
      '@type': 'Offer',
      priceCurrency: 'AUD',
      availability: 'https://schema.org/InStock',
      validFrom: '2024-01-01'
    }
  };

  // Generate FAQ items for schema if available
  const faqItems = [
    {
      question: `What is ${service.title}?`,
      answer: service.longDescription || service.description
    },
    {
      question: `How can ${service.title} help my business?`,
      answer: `Our ${service.title.toLowerCase()} solutions help Melbourne businesses ${service.benefits[0].toLowerCase()} and ${service.benefits[1].toLowerCase()}.`
    },
    {
      question: 'How long does implementation take?',
      answer: `Implementation time varies based on your specific needs, but typically our ${service.title.toLowerCase()} solutions can be set up within 2-4 weeks.`
    },
    {
      question: 'Do you provide support after implementation?',
      answer: 'Yes, we provide ongoing support to ensure your automation solutions continue to work effectively for your business.'
    }
  ];

  // For other services, render the dynamic service page
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <PageHeader
        title={service.title}
        description={service.description}
      />

      {/* Features Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Key Features</h2>
            <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
              Our {service.title.toLowerCase()} solutions provide powerful features to transform your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {service.features.map((feature, index) => {
              // Determine which icon to use
              const IconComponent = feature.icon && iconMap[feature.icon] ? iconMap[feature.icon] : Cog;

              return (
                <div
                  key={index}
                  className="p-6 bg-secondary/5 border border-border/30 rounded-xl hover:border-primary/20 transition-colors"
                >
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                    <IconComponent className="w-6 h-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                  <p className="text-foreground/60">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </Container>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-secondary/5">
        <Container>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Benefits for Your Business</h2>
              <p className="text-xl text-foreground/60 mb-8">
                Our {service.title.toLowerCase()} solutions deliver practical benefits for businesses of all sizes.
              </p>

              <ul className="space-y-4">
                {service.benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-primary flex-shrink-0 mt-0.5" />
                    <span className="text-lg">{benefit}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-10">
                <Link
                  href="/consultation"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Book a Free Consultation
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl transform rotate-3 scale-95 blur-xl opacity-30" />
              <div className="relative bg-background border border-border/30 rounded-3xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold mb-4">Client Success Story</h3>
                <p className="text-foreground/70 mb-6">
                  "Advisync's {service.title.toLowerCase()} solutions have transformed how we operate. We've seen significant improvements in efficiency and reduced operational costs."
                </p>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Business Owner</p>
                    <p className="text-sm text-foreground/60">Melbourne, Australia</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Implementation Process</h2>
            <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
              We make it easy to get started with {service.title.toLowerCase()} for your business.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {service.process.map((step, index) => (
              <div
                key={index}
                className="p-6 bg-background border border-border/30 rounded-xl relative"
              >
                <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold">
                  {index + 1}
                </div>
                <h3 className="text-xl font-semibold mb-3 mt-2">{step.title}</h3>
                <p className="text-foreground/60">{step.description}</p>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Pricing Section */}
      {service.pricing && service.pricing.length > 0 && (
        <section className="py-20 bg-secondary/5">
          <Container>
            <div className="text-center mb-16">
              <h2 className="text-3xl font-bold mb-4">Pricing Options</h2>
              <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
                Choose the plan that best fits your business needs.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
              {service.pricing.map((price, index) => (
                <div
                  key={index}
                  className="p-8 bg-background border border-border/30 rounded-xl hover:border-primary/20 transition-colors"
                >
                  <h3 className="text-2xl font-bold mb-2">{price.name}</h3>
                  <p className="text-foreground/60 mb-4">{price.description}</p>
                  <div className="mb-6">
                    <span className="text-3xl font-bold text-accent-secondary">${price.price}</span>
                    <span className="text-foreground/60"> AUD</span>
                  </div>
                  <ul className="space-y-3 mb-8">
                    {price.features.map((feature, idx) => (
                      <li key={idx} className="flex items-start gap-3">
                        <CheckCircle className="w-5 h-5 text-accent-secondary flex-shrink-0 mt-0.5" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                  <Link
                    href="/consultation"
                    className="inline-flex items-center justify-center w-full gap-2 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                  >
                    Get Started
                    <ArrowRight className="w-4 h-4" />
                  </Link>
                </div>
              ))}
            </div>
          </Container>
        </section>
      )}

      {/* FAQ Section */}
      <section className="py-20">
        <Container>
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">
              Frequently Asked Questions
            </h2>
            <p className="text-foreground/60 text-center mb-12 max-w-3xl mx-auto">
              Common questions about our {service.title.toLowerCase()} solutions
            </p>

            <Accordion type="single" collapsible className="w-full space-y-4">
              {faqItems.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`faq-item-${index}`}
                  className="border border-border/30 rounded-lg px-6 py-2 bg-background/50"
                >
                  <AccordionTrigger className="text-lg font-medium text-left">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-foreground/70 pt-2 pb-4">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">Ready to Transform Your Business?</h2>
            <p className="text-xl text-foreground/70 mb-8">
              Book a consultation to discuss how our {service.title.toLowerCase()} solutions can help your business grow and succeed.
            </p>
            <Link
              href="/consultation"
              className="inline-flex items-center gap-2 px-8 py-4 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-lg font-medium"
            >
              Free Consultation
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </Container>
      </section>
    </main>
  );
}