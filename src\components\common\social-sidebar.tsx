'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { Facebook, Twitter, Instagram, Linkedin } from 'lucide-react';

const socialLinks = [
  { 
    icon: Facebook, 
    href: "https://www.facebook.com/profile.php?id=61573127892569", 
    label: "Facebook",
    color: "hover:bg-blue-600"
  },
  { 
    icon: Twitter, 
    href: "https://x.com/Advisync_AI_Sol", 
    label: "X",
    color: "hover:bg-black"
  },
  { 
    icon: Instagram, 
    href: "https://www.instagram.com/advisync_solutions_/", 
    label: "Instagram",
    color: "hover:bg-gradient-to-r hover:from-purple-500 hover:via-pink-500 hover:to-orange-500"
  },
  { 
    icon: Linkedin, 
    href: "https://www.linkedin.com/company/advisync-solutions", 
    label: "LinkedIn",
    color: "hover:bg-blue-700"
  }
];

export function SocialSidebar() {
  return (
    <>
      {/* Desktop version - Sidebar */}
      <div className="fixed right-4 top-1/2 -translate-y-1/2 z-50 hidden md:flex flex-col gap-3">
        {socialLinks.map((social) => (
          <motion.a
            key={social.label}
            href={social.href}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={social.label}
            className={`w-10 h-10 rounded-full bg-secondary/5 backdrop-blur-sm border border-white/10 flex items-center justify-center ${social.color} transition-colors group touch-manipulation shadow-md hover:shadow-lg hover:text-white`}
            whileHover={{ scale: 1.1, y: -2 }}
            whileTap={{ scale: 0.95 }}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.3, delay: 0.1 * socialLinks.indexOf(social) }}
          >
            <social.icon 
              size={18} 
              className="text-foreground/70 group-hover:text-white transition-colors" 
            />
            <span className="absolute right-full mr-2 px-2 py-1 rounded bg-secondary/90 backdrop-blur-sm text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
              {social.label}
            </span>
          </motion.a>
        ))}
      </div>

      {/* Mobile version - Bottom bar */}
      <div className="fixed bottom-0 left-0 right-0 z-40 md:hidden bg-background/70 backdrop-blur-md border-t border-white/10 py-2 px-4 mb-safe">
        <div className="flex justify-around items-center gap-2 pb-2">
          {socialLinks.map((social) => (
            <motion.a
              key={social.label}
              href={social.href}
              target="_blank"
              rel="noopener noreferrer"
              aria-label={social.label}
              className={`w-8 h-8 rounded-full bg-secondary/5 flex items-center justify-center ${social.color} transition-colors group touch-manipulation`}
              whileTap={{ scale: 0.95 }}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * socialLinks.indexOf(social) }}
            >
              <social.icon 
                size={16} 
                className="text-foreground/70 group-hover:text-white transition-colors" 
              />
            </motion.a>
          ))}
          <span className="text-xs text-white/50">Connect with us</span>
        </div>
      </div>
    </>
  );
} 