'use client';

import { useEffect, useRef, useState } from 'react';
import { contactInfo } from '@/config/contact';
import { cn } from '@/lib/utils';

interface CalendlyWidgetProps {
  url?: string;
  className?: string;
  height?: number;
  prefill?: {
    name?: string;
    email?: string;
    customAnswers?: Record<string, string>;
  };
  utm?: {
    utmCampaign?: string;
    utmSource?: string;
    utmMedium?: string;
    utmContent?: string;
    utmTerm?: string;
  };
}

export function CalendlyWidget({
  url = contactInfo.calendly,
  className,
  height = 700,
  prefill,
  utm
}: CalendlyWidgetProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [isError, setIsError] = useState(false);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    // Load Calendly script
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    script.onload = () => setIsLoaded(true);
    script.onerror = () => setIsError(true);
    document.body.appendChild(script);

    return () => {
      // Clean up script on unmount
      document.body.removeChild(script);
    };
  }, []);

  // Construct URL with prefill parameters if provided
  const constructUrl = () => {
    const baseUrl = url;
    const params = new URLSearchParams();

    // Add prefill parameters
    if (prefill) {
      if (prefill.name) params.append('name', prefill.name);
      if (prefill.email) params.append('email', prefill.email);

      // Add custom answers
      if (prefill.customAnswers) {
        Object.entries(prefill.customAnswers).forEach(([key, value]) => {
          params.append(`a1=${key}`, value);
        });
      }
    }

    // Add UTM parameters
    if (utm) {
      if (utm.utmCampaign) params.append('utm_campaign', utm.utmCampaign);
      if (utm.utmSource) params.append('utm_source', utm.utmSource);
      if (utm.utmMedium) params.append('utm_medium', utm.utmMedium);
      if (utm.utmContent) params.append('utm_content', utm.utmContent);
      if (utm.utmTerm) params.append('utm_term', utm.utmTerm);
    }

    // Return URL with parameters if any
    const paramsString = params.toString();
    return paramsString ? `${baseUrl}?${paramsString}` : baseUrl;
  };

  return (
    <div className={cn("w-full overflow-hidden rounded-xl border border-primary/20 shadow-glow-sm", className)}>
      {isError ? (
        <div className="flex flex-col items-center justify-center p-8 text-center bg-background/30 backdrop-blur-sm h-[400px]">
          <p className="text-white/80 mb-4">
            Unable to load the scheduling widget. Please try again later or use the direct link below.
          </p>
          <a
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors border border-primary/30 shadow-glow-sm"
          >
            Schedule a Meeting
          </a>
        </div>
      ) : (
        <div
          className="calendly-inline-widget"
          data-url={constructUrl()}
          style={{ minWidth: '320px', height: `${height}px` }}
        />
      )}
    </div>
  );
}
