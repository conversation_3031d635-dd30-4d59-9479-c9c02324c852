'use client';

import { MapPin } from 'lucide-react';

export function MapSection() {
  return (
    <div className="relative w-full h-[400px] rounded-lg overflow-hidden">
      <iframe 
        src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3152.2977365574307!2d144.96221661531773!3d-37.81379797975171!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x6ad65d4c2b349649%3A0xb6899234e561db11!2sMelbourne%20VIC%203000!5e0!3m2!1sen!2sau!4v1645167712261!5m2!1sen!2sau" 
        width="100%" 
        height="100%" 
        style={{ border: 0 }} 
        allowFullScreen={false} 
        loading="lazy" 
        referrerPolicy="no-referrer-when-downgrade"
        title="Advisync Solutions Melbourne Office Location"
        className="absolute inset-0"
      />
      <div className="absolute inset-0 bg-gradient-to-br from-black/50 to-black/30 flex flex-col items-center justify-center">
        <div className="bg-background/90 backdrop-blur-sm p-6 rounded-lg shadow-lg">
          <div className="flex items-center justify-center mb-4">
            <MapPin className="h-6 w-6 text-primary mr-2" />
            <h3 className="text-xl font-medium">Advisync Melbourne</h3>
          </div>
          <p className="text-foreground/70 text-center">
            123 Business Street<br />
            Melbourne, VIC 3000<br />
            Australia
          </p>
          <div className="mt-6 flex justify-center">
            <a 
              href="https://maps.google.com/?q=Melbourne+VIC+3000+Australia" 
              target="_blank" 
              rel="noopener noreferrer"
              className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90 transition-colors"
            >
              Get Directions
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 