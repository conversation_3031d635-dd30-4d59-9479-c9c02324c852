interface LocalBusinessSchemaProps {
  name?: string;
  description?: string;
  address?: {
    streetAddress?: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  phone?: string;
  email?: string;
  website?: string;
  abn?: string;
  openingHours?: string[];
  services?: string[];
  priceRange?: string;
  geo?: {
    latitude: number;
    longitude: number;
  };
}

export function LocalBusinessSchema({
  name = 'Advisync Solutions',
  description = 'Australian AI-automation agency specializing in voice agents, virtual receptionists, and workflow automation for businesses.',
  address = {
    addressLocality: 'Melbourne',
    addressRegion: 'VIC',
    postalCode: '3000',
    addressCountry: 'AU'
  },
  phone = '+61-***********',
  email = '<EMAIL>',
  website = 'https://advisync.com.au',
  abn = '12 ***********',
  openingHours = [
    'Mo-Fr 09:00-17:00'
  ],
  services = [
    'AI Voice Agents',
    'Virtual Receptionist',
    'Workflow Automation',
    'Business Process Automation',
    'Customer Service Automation',
    'Lead Management Automation'
  ],
  priceRange = '$$',
  geo = {
    latitude: -37.8136,
    longitude: 144.9631
  }
}: LocalBusinessSchemaProps) {
  const schema = {
    '@context': 'https://schema.org',
    '@type': 'LocalBusiness',
    '@id': `${website}#organization`,
    name,
    description,
    url: website,
    logo: {
      '@type': 'ImageObject',
      url: `${website}/images/advisync-logo.png`,
      width: 200,
      height: 60
    },
    image: {
      '@type': 'ImageObject',
      url: `${website}/images/og/home-og.jpg`,
      width: 1200,
      height: 630
    },
    address: {
      '@type': 'PostalAddress',
      ...address
    },
    geo: {
      '@type': 'GeoCoordinates',
      latitude: geo.latitude,
      longitude: geo.longitude
    },
    contactPoint: [
      {
        '@type': 'ContactPoint',
        telephone: phone,
        contactType: 'customer service',
        email,
        availableLanguage: ['English'],
        areaServed: 'AU'
      }
    ],
    openingHoursSpecification: openingHours.map(hours => ({
      '@type': 'OpeningHoursSpecification',
      dayOfWeek: hours.split(' ')[0].split('-').length > 1 
        ? hours.split(' ')[0].split('-').map(day => {
            const dayMap: Record<string, string> = {
              'Mo': 'Monday',
              'Tu': 'Tuesday', 
              'We': 'Wednesday',
              'Th': 'Thursday',
              'Fr': 'Friday',
              'Sa': 'Saturday',
              'Su': 'Sunday'
            };
            return dayMap[day] || day;
          })
        : [hours.split(' ')[0]],
      opens: hours.split(' ')[1]?.split('-')[0] || '09:00',
      closes: hours.split(' ')[1]?.split('-')[1] || '17:00'
    })),
    priceRange,
    currenciesAccepted: 'AUD',
    paymentAccepted: 'Cash, Credit Card, Bank Transfer',
    areaServed: {
      '@type': 'Country',
      name: 'Australia'
    },
    serviceArea: {
      '@type': 'GeoCircle',
      geoMidpoint: {
        '@type': 'GeoCoordinates',
        latitude: geo.latitude,
        longitude: geo.longitude
      },
      geoRadius: '50000' // 50km radius
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: 'AI Automation Services',
      itemListElement: services.map((service, index) => ({
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: service,
          description: `Professional ${service.toLowerCase()} services for Australian businesses`
        }
      }))
    },
    sameAs: [
      'https://www.linkedin.com/company/advisync-solutions',
      'https://x.com/Advisync_AI_Sol',
      'https://www.facebook.com/profile.php?id=**************',
      'https://www.instagram.com/advisync_solutions_/'
    ],
    taxID: abn,
    vatID: abn,
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.9',
      reviewCount: '47',
      bestRating: '5',
      worstRating: '1'
    }
  };

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
    />
  );
}
