import type { Metadata } from 'next';
import { Container } from '@/components/common/container';
import {
  Cog,
  Calendar,
  MessageSquare,
  FileText,
  Bot,
  ArrowRight,
  LucideIcon,
  Users,
  ClipboardCheck,
  Mail,
  Heart
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Business Automation Services | Advisync Solutions Melbourne',
  description: 'Explore our comprehensive business automation services including NDIS provider solutions, workflow optimization, and digital transformation. Tailored solutions for Melbourne businesses.',
  keywords: [
    'business automation services',
    'NDIS provider solutions',
    'workflow automation',
    'digital transformation services',
    'Melbourne automation experts',
    'business process optimization',
    'small business automation',
    'Melbourne digital solutions'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/services'
  },
  openGraph: {
    title: 'Business Automation Services | Advisync Solutions',
    description: 'Transform your business with our comprehensive automation and digital solutions. Expert services tailored for Melbourne businesses and NDIS providers.',
    url: 'https://advisync.com.au/services',
    type: 'website',
    siteName: 'Advisync Solutions',
    locale: 'en_AU',
    images: [
      {
        url: '/images/og/services-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync Solutions Business Automation Services'
      }
    ]
  }
};

interface ServiceFeature {
  icon: LucideIcon;
  title: string;
  description: string;
}

interface Service {
  id: string;
  title: string;
  description: string;
  features: ServiceFeature[];
  mainIcon: LucideIcon;
  benefits: string[];
  processSteps: {
    title: string;
    description: string;
  }[];
  caseStudies: {
    title: string;
    result: string;
  }[];
}

const services: Service[] = [
  {
    id: 'ai-voice-agents',
    title: 'AI Voice Agents',
    description: 'Perfect for tradies, healthcare providers, and NDIS services. 24/7 AI receptionists that never miss a call, book appointments, and handle customer inquiries with authentic Australian voices.',
    features: [
      {
        icon: Bot,
        title: '24/7 Call Answering',
        description: 'AI receptionist answers every call with a professional Australian voice'
      },
      {
        icon: Calendar,
        title: 'Appointment Booking',
        description: 'Books appointments directly into your calendar with confirmations'
      },
      {
        icon: Users,
        title: 'Lead Information Capture',
        description: 'Collects customer details and forwards to you via email or SMS'
      },
      {
        icon: MessageSquare,
        title: 'Basic Customer Service',
        description: 'Answers common questions about services, pricing, and hours'
      }
    ],
    mainIcon: Bot,
    benefits: [
      'Never miss a call again (even when you\'re on a job)',
      'Capture leads 24/7 without hiring staff',
      'Professional first impression for every caller',
      'Book appointments while you sleep'
    ],
    processSteps: [
      {
        title: 'Discovery Call',
        description: 'We understand your business needs and common customer inquiries.'
      },
      {
        title: 'Voice Setup',
        description: 'We configure your AI agent with Australian voice and your business info.'
      },
      {
        title: 'Integration',
        description: 'We connect the AI agent to your phone system and calendar.'
      },
      {
        title: 'Training & Launch',
        description: 'We test everything and train you on managing your AI receptionist.'
      }
    ],
    caseStudies: [
      {
        title: 'Frankston Electrician',
        result: '100% emergency calls captured after hours'
      },
      {
        title: 'Box Hill Physiotherapy',
        result: '95% reduction in missed calls'
      }
    ]
  },
  {
    id: 'business-automation',
    title: 'AI Workflow Automation',
    description: 'Ideal for busy tradies, healthcare practices, and NDIS providers. Simple automation solutions that connect your business tools and eliminate repetitive paperwork.',
    features: [
      {
        icon: Cog,
        title: 'Form to Email Automation',
        description: 'Automatically send form submissions to your email or CRM system'
      },
      {
        icon: MessageSquare,
        title: 'Appointment Reminders',
        description: 'Send automatic SMS and email reminders to reduce no-shows'
      },
      {
        icon: FileText,
        title: 'Basic Document Generation',
        description: 'Create simple quotes, invoices, or reports from form data'
      },
      {
        icon: Mail,
        title: 'Email Follow-ups',
        description: 'Automated email sequences for customer follow-ups and check-ins'
      }
    ],
    mainIcon: Cog,
    benefits: [
      'Save 5-10 hours per week on repetitive tasks',
      'Reduce manual data entry errors',
      'Improve customer communication',
      'Simple setup that actually works'
    ],
    processSteps: [
      {
        title: 'Process Review',
        description: 'We identify which repetitive tasks can be automated.'
      },
      {
        title: 'Simple Design',
        description: 'We design basic automation workflows that are easy to understand.'
      },
      {
        title: 'Quick Setup',
        description: 'We implement simple automation solutions using proven tools.'
      },
      {
        title: 'Basic Training',
        description: 'We show you how to manage and modify your automations.'
      }
    ],
    caseStudies: [
      {
        title: 'Local Builder',
        result: '8 hours saved per week on quotes'
      },
      {
        title: 'Dental Practice',
        result: '50% reduction in no-shows'
      }
    ]
  },
  {
    id: 'customer-management',
    title: 'Customer Management Automation',
    description: 'Perfect for service businesses that want to capture every lead and keep customers happy. Automated follow-ups, appointment management, and customer communication that works alongside your AI voice agents.',
    features: [
      {
        icon: Users,
        title: 'Lead Capture & Follow-up',
        description: 'Automatically capture leads and follow up to convert them into customers'
      },
      {
        icon: MessageSquare,
        title: 'Customer Communication',
        description: 'Automated SMS, emails, and reminders to keep customers engaged'
      },
      {
        icon: Calendar,
        title: 'Appointment Management',
        description: 'Streamline scheduling, confirmations, and reminders to reduce no-shows'
      },
      {
        icon: FileText,
        title: 'Customer Data Organization',
        description: 'Keep all customer information organized and accessible in one place'
      }
    ],
    mainIcon: Heart,
    benefits: [
      'Never lose a lead again',
      'Increase customer conversion by 40%',
      'Automate follow-ups that actually work',
      'Turn more inquiries into paying customers'
    ],
    processSteps: [
      {
        title: 'Customer Journey Analysis',
        description: 'We analyze how customers interact with your business to find automation opportunities.'
      },
      {
        title: 'Automation Design',
        description: 'We design customer management workflows that work with your AI voice agents.'
      },
      {
        title: 'Setup & Integration',
        description: 'We set up the systems and integrate them with your current customer touchpoints.'
      },
      {
        title: 'Training & Optimization',
        description: 'We train your team and optimize the system to maximize customer conversion.'
      }
    ],
    caseStudies: [
      {
        title: 'Dandenong Plumber',
        result: '55% increase in quote conversions'
      },
      {
        title: 'Geelong NDIS Provider',
        result: '80% reduction in admin time'
      }
    ]
  }
];


// Add JSON-LD structured data
const getJsonLd = (servicesList: Service[]) => ({
  '@context': 'https://schema.org',
  '@type': 'ItemList',
  itemListElement: servicesList.map((service, index) => ({
    '@type': 'Service',
    '@id': `https://advisync.com.au/services#${service.id}`,
    position: index + 1,
    name: service.title,
    description: service.description,
    provider: {
      '@type': 'Organization',
      name: 'Advisync Solutions',
      url: 'https://advisync.com.au'
    },
    areaServed: {
      '@type': 'City',
      name: 'Melbourne',
      '@id': 'https://www.wikidata.org/wiki/Q3141'
    },
    hasOfferCatalog: {
      '@type': 'OfferCatalog',
      name: `${service.title} Features`,
      itemListElement: service.features.map((feature, i) => ({
        '@type': 'Offer',
        itemOffered: {
          '@type': 'Service',
          name: feature.title
        },
        position: i + 1
      }))
    }
  }))
});

export default function ServicesPage() {
  return (
    <main className="py-10 md:py-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(getJsonLd(services)) }}
      />
      <Container>
        {/* Hero Section - Royal Sapphire & Gold Design */}
        <section className="text-center mb-16 md:mb-32 bg-bg-primary py-12 md:py-20 px-4 rounded-2xl">
          <div className="max-w-6xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold mb-4 md:mb-6 text-text-heading">
              Our <span className="text-accent-secondary">Services</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-text-body max-w-3xl mx-auto mb-6 md:mb-12 leading-relaxed">
              Transform your business with AI voice agents and intelligent automation solutions.
              Perfect for <span className="text-accent-secondary font-medium">tradies</span> (electricians, plumbers, builders),
              <span className="text-accent-secondary font-medium"> healthcare providers</span> (chiropractors, physios, dentists),
              and <span className="text-accent-secondary font-medium">NDIS services</span> who want to save time and never miss a call.
            </p>
            <div className="flex flex-wrap justify-center gap-3 md:gap-4">
              <div className="flex items-center gap-2 px-3 md:px-4 py-1.5 md:py-2 bg-accent-primary/10 border border-accent-primary/20 rounded-full">
                <span className="w-2 h-2 bg-accent-primary rounded-full animate-pulse" />
                <span className="text-xs md:text-sm text-accent-primary font-medium">Expert Solutions</span>
              </div>
              <div className="flex items-center gap-2 px-3 md:px-4 py-1.5 md:py-2 bg-accent-secondary/10 border border-accent-secondary/20 rounded-full">
                <span className="w-2 h-2 bg-accent-secondary rounded-full animate-pulse" />
                <span className="text-xs md:text-sm text-accent-secondary font-medium">Melbourne Based</span>
              </div>
            </div>
          </div>
        </section>

        {/* Services Grid */}
        <div className="space-y-16 md:space-y-32 mb-16 md:mb-20">
          {services.map((service) => (
            <section 
              key={service.id}
              className="relative group"
            >
              <div className="bg-surface-card p-6 md:p-12 rounded-2xl border border-border-hair hover:border-accent-primary/30 transition-colors">
                {/* Service Header */}
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 md:gap-6 mb-8 md:mb-12">
                  <div className="w-12 h-12 md:w-16 md:h-16 rounded-2xl bg-accent-primary/10 flex items-center justify-center">
                    <service.mainIcon className="w-6 h-6 md:w-8 md:h-8 text-accent-primary" />
                  </div>
                  <div>
                    <h2 className="text-2xl md:text-3xl font-bold mb-2 text-text-heading">{service.title}</h2>
                    <p className="text-text-body text-base md:text-lg">{service.description}</p>
                  </div>
                </div>

                {/* Features Grid */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-10 md:mb-16">
                  {service.features.map((feature) => (
                    <div 
                      key={feature.title}
                      className="group/feature bg-bg-primary rounded-2xl p-4 md:p-6 border border-border-hair hover:border-accent-primary/30 transition-all duration-300 hover:translate-y-[-2px]"
                    >
                      <div className="w-10 h-10 md:w-12 md:h-12 rounded-xl bg-accent-primary/10 flex items-center justify-center mb-3 md:mb-4 transform group-hover/feature:scale-105 transition-transform duration-300">
                        <feature.icon className="w-5 h-5 md:w-6 md:h-6 text-accent-primary" />
                      </div>
                      <h3 className="text-base md:text-lg font-semibold mb-1 md:mb-2 text-text-heading">{feature.title}</h3>
                      <p className="text-xs md:text-sm text-text-body">{feature.description}</p>
                    </div>
                  ))}
                </div>

                {/* Process Steps */}
                <div className="mb-10 md:mb-16">
                  <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-8">Our Process</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                    {service.processSteps.map((step, stepIndex) => (
                      <div 
                        key={step.title}
                        className="bg-secondary/5 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-primary/10 relative group/step hover:border-primary/20 transition-all duration-300"
                      >
                        <div className="absolute -top-3 md:-top-4 left-3 md:left-4 w-6 h-6 md:w-8 md:h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm md:text-base font-bold group-hover/step:scale-110 transition-transform">
                          {stepIndex + 1}
                        </div>
                        <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2 mt-2">{step.title}</h4>
                        <p className="text-xs md:text-sm text-foreground/60">{step.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Case Studies */}
                <div>
                  <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-8">Success Stories</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 md:gap-6">
                    {service.caseStudies.map((study) => (
                      <div 
                        key={study.title}
                        className="group/case bg-secondary/5 backdrop-blur-sm rounded-2xl p-4 md:p-6 border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:translate-y-[-4px]"
                      >
                        <h4 className="text-base md:text-lg font-semibold mb-1 md:mb-2">{study.title}</h4>
                        <p className="text-primary text-sm md:text-base font-medium group-hover/case:translate-x-2 transition-transform">
                          {study.result}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </section>
          ))}
        </div>

        {/* CTA Section */}
        <section className="bg-surface-card rounded-2xl p-6 md:p-16 border border-border-hair">
          <div className="text-center">
            <h2 className="text-2xl md:text-4xl font-bold mb-4 md:mb-6 text-text-heading">
              Ready to Transform Your <span className="text-accent-secondary">Business?</span>
            </h2>
            <p className="text-base md:text-xl text-text-body mb-8 md:mb-12 max-w-2xl mx-auto">
              Get in touch with us today to discuss how our AI voice agents and automation solutions can help
              your business save time and never miss another call.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link
                href="/consultation"
                className="w-full sm:w-auto group inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 rounded-full bg-primary text-white hover:bg-primary/90 transition-all duration-300 transform hover:scale-105"
              >
                <span className="font-medium">Free Consultation</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 ml-2 transform transition-transform group-hover:translate-x-1" />
              </Link>
              <Link
                href="/case-studies"
                className="w-full sm:w-auto group inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 rounded-full bg-secondary/10 text-secondary hover:bg-secondary/20 transition-all duration-300 transform hover:scale-105"
              >
                <span className="font-medium">View Success Stories</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 ml-2 transform transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
            <p className="mt-4 md:mt-6 text-xs md:text-sm text-foreground/40">
              No obligations, no pressure - just practical solutions for your business
            </p>
          </div>
        </section>
      </Container>
    </main>
  );
} 