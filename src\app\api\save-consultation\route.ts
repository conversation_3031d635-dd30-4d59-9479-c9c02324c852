import { NextRequest, NextResponse } from 'next/server';
import { sendConsultationToN8N } from '@/lib/n8n';

export async function POST(request: NextRequest) {
  try {
    const data = await request.json();
    
    // Log the submission data to the console (for development)
    console.log('Consultation form submission received:', data);
    
    const { 
      name, 
      email, 
      phone, 
      company, 
      teamSize, 
      service, 
      serviceName,
      serviceCategory,
      time, 
      message,
      submitDate 
    } = data;

    // Send data to N8N workflow
    const n8nResult = await sendConsultationToN8N({
      name,
      email,
      phone,
      company,
      teamSize,
      service,
      serviceName,
      serviceCategory,
      time,
      message,
      submitDate
    });

    if (!n8nResult.success) {
      console.error('N8N webhook failed:', n8nResult.error);
      // Continue with success response - don't block user experience
    }
    
    return NextResponse.json({
      success: true,
      message: 'Thank you for your submission. We\'ll be in touch shortly!'
    });
  } catch (error) {
    console.error('Error processing consultation form submission:', error);
    return NextResponse.json(
      {
        success: false,
        message: 'There was an error processing your submission. Please try again later.'
      },
      { status: 500 }
    );
  }
}

// For GET requests, return a message that this endpoint only accepts POST
export async function GET() {
  return NextResponse.json(
    {
      success: false,
      message: 'This endpoint only accepts POST requests with consultation form data.'
    },
    { status: 405 }
  );
} 