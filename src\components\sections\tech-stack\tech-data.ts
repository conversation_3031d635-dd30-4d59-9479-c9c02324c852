interface TechItem {
  name: string;
  description: string;
  icon: string;
  category: 'frontend' | 'backend' | 'automation' | 'ai';
}

export const techStack: TechItem[] = [
  {
    name: 'Next.js',
    description: 'React framework for production-grade applications',
    icon: '/icons/nextjs.svg',
    category: 'frontend'
  },
  {
    name: 'React',
    description: 'JavaScript library for building user interfaces',
    icon: '/icons/react.svg',
    category: 'frontend'
  },
  {
    name: 'TypeScript',
    description: 'Typed superset of JavaScript for better development',
    icon: '/icons/typescript.svg',
    category: 'frontend'
  },
  {
    name: 'Tailwind CSS',
    description: 'Utility-first CSS framework',
    icon: '/icons/tailwind.svg',
    category: 'frontend'
  },
  {
    name: 'Node.js',
    description: 'JavaScript runtime for server-side applications',
    icon: '/icons/nodejs.svg',
    category: 'backend'
  },

  {
    name: 'Make.com',
    description: 'Advanced automation platform',
    icon: '/icons/make.svg',
    category: 'automation'
  },
  {
    name: 'OpenAI',
    description: 'AI-powered solutions and integrations',
    icon: '/icons/openai.svg',
    category: 'ai'
  }
]; 