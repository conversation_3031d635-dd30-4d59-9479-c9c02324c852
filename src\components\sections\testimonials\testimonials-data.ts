interface Testimonial {
  id: string;
  name: string;
  role: string;
  company: string;
  companyLogo: string;
  content: string;
  rating: number;
  image?: string;
}

export const testimonials: Testimonial[] = [
  {
    id: '1',
    name: '<PERSON>',
    role: 'Business Owner',
    company: 'Melbourne Wellness Center',
    companyLogo: '/logos/melbourne-wellness.svg',
    content: 'Advisync Solutions transformed our booking system and client communications. Their automation solutions saved us countless hours, and their local support team is always there when we need them.',
    rating: 5,
    image: '/images/testimonials/sarah.jpg'
  },
  {
    id: '2',
    name: '<PERSON>',
    role: 'Managing Director',
    company: 'Melbourne Digital Services',
    companyLogo: '/logos/melbourne-digital.svg',
    content: 'The team at Advisync truly understands Melbourne businesses. Their intelligent automation solutions helped us streamline operations and improve customer service significantly.',
    rating: 5,
    image: '/images/testimonials/david.jpg'
  },
  {
    id: '3',
    name: '<PERSON>',
    role: 'Operations Manager',
    company: 'South Melbourne Retail',
    companyLogo: '/logos/south-melbourne.svg',
    content: 'Implementing Advisync\'s automation solutions streamlined our workflow and reduced manual tasks by 70%. Their Melbourne-based support team is exceptional.',
    rating: 5,
    image: '/images/testimonials/emma.jpg'
  },
  {
    id: '4',
    name: '<PERSON>',
    role: 'CEO',
    company: 'Melbourne Tech Solutions',
    companyLogo: '/logos/melbourne-tech.svg',
    content: 'Advisync\'s web development and automation services helped us establish a strong digital presence. Their expertise in AI and local business understanding is unmatched.',
    rating: 5,
    image: '/images/testimonials/michael.jpg'
  }
]; 