{"name": "advisync-ai-solutions", "version": "0.1.0", "private": true, "description": "Melbourne's trusted digital automation partner, helping small businesses transform their operations through intelligent automation and custom digital solutions.", "scripts": {"dev": "next dev", "build": "next build && npm run generate-sitemap", "build:analyze": "ANALYZE=true next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "performance": "npm run build && npm run start", "download-images": "node src/scripts/download-blog-images.js", "download-case-studies": "node src/scripts/download-case-study-images.js", "check-subscribers": "node src/scripts/subscriber-milestone.js", "init-subscribers": "node src/scripts/check-and-initialize-subscribers.js", "generate-sitemap": "node src/scripts/generate-sitemap.js", "generate-og": "node src/scripts/generate-og-images.js"}, "dependencies": {"@hookform/resolvers": "^4.1.0", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tabs": "^1.1.3", "@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "autoprefixer": "^10.4.17", "canvas": "^3.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "file-loader": "^6.2.0", "framer-motion": "^11.18.2", "gsap": "^3.12.5", "lucide-react": "^0.316.0", "next": "^14.2.29", "nodemailer": "^6.10.0", "postcss": "^8.4.33", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.54.2", "react-tsparticles": "^2.12.0", "sharp": "^0.33.5", "tailwind-merge": "^2.6.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "tsparticles": "^2.12.0", "tsparticles-slim": "^2.12.0", "typescript": "^5.3.3", "url-loader": "^4.1.1", "zod": "^3.24.2"}, "devDependencies": {"@next/bundle-analyzer": "^14.2.29", "@tailwindcss/typography": "^0.5.16", "@types/nodemailer": "^6.4.17", "@typescript-eslint/eslint-plugin": "^6.19.1", "@typescript-eslint/parser": "^6.19.1", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "globby": "^14.1.0", "prettier": "^3.5.3", "ts-node": "^10.9.2"}}