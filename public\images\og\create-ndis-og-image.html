<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>NDIS OG Image Generator</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      background-color: #f5f5f5;
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    
    #canvas-container {
      position: relative;
      width: 1200px;
      height: 630px;
      overflow: hidden;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    canvas {
      display: block;
    }
    
    .controls {
      margin-top: 20px;
      text-align: center;
    }
    
    button {
      padding: 10px 20px;
      background-color: #6366F1;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
    }
    
    button:hover {
      background-color: #4F46E5;
    }
  </style>
</head>
<body>
  <div>
    <div id="canvas-container">
      <canvas id="ogImage" width="1200" height="630"></canvas>
    </div>
    <div class="controls">
      <button id="downloadBtn">Download Image</button>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const canvas = document.getElementById('ogImage');
      const ctx = canvas.getContext('2d');
      
      // Background gradient
      const gradient = ctx.createLinearGradient(0, 0, 1200, 630);
      gradient.addColorStop(0, '#030712');
      gradient.addColorStop(0.5, '#111827');
      gradient.addColorStop(1, '#030712');
      ctx.fillStyle = gradient;
      ctx.fillRect(0, 0, 1200, 630);
      
      // Add decorative elements
      ctx.fillStyle = '#6366F1';
      ctx.globalAlpha = 0.1;
      ctx.beginPath();
      ctx.arc(1100, 100, 300, 0, Math.PI * 2);
      ctx.fill();
      
      ctx.fillStyle = '#22D3EE';
      ctx.globalAlpha = 0.1;
      ctx.beginPath();
      ctx.arc(100, 500, 250, 0, Math.PI * 2);
      ctx.fill();
      
      // Reset opacity
      ctx.globalAlpha = 1;
      
      // Add border
      ctx.strokeStyle = '#6366F1';
      ctx.lineWidth = 4;
      ctx.strokeRect(20, 20, 1160, 590);
      
      // Add title
      ctx.fillStyle = '#F9FAFB';
      ctx.font = 'bold 60px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.textAlign = 'center';
      ctx.fillText('NDIS Provider Solutions', 600, 250);
      
      // Add subtitle
      ctx.fillStyle = '#9CA3AF';
      ctx.font = '32px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillText('Specialized Automation for Melbourne NDIS Providers', 600, 320);
      
      // Add company name
      ctx.fillStyle = '#6366F1';
      ctx.font = 'bold 40px system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
      ctx.fillText('Advisync Solutions', 600, 500);
      
      // Add icon placeholders
      function drawIcon(x, y, size) {
        ctx.fillStyle = '#6366F1';
        ctx.globalAlpha = 0.2;
        ctx.beginPath();
        ctx.arc(x, y, size, 0, Math.PI * 2);
        ctx.fill();
        ctx.globalAlpha = 1;
        ctx.strokeStyle = '#6366F1';
        ctx.lineWidth = 2;
        ctx.stroke();
      }
      
      drawIcon(300, 400, 30);
      drawIcon(450, 400, 30);
      drawIcon(600, 400, 30);
      drawIcon(750, 400, 30);
      drawIcon(900, 400, 30);
      
      // Download functionality
      document.getElementById('downloadBtn').addEventListener('click', function() {
        const link = document.createElement('a');
        link.download = 'ndis-services-og.jpg';
        link.href = canvas.toDataURL('image/jpeg', 0.9);
        link.click();
      });
    });
  </script>
</body>
</html> 