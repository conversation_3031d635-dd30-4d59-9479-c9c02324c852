'use client';

import { useState, useEffect } from 'react';
import { Calendar, X } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FloatingCalendlyButtonProps {
  className?: string;
}

export function FloatingCalendlyButton({ className }: FloatingCalendlyButtonProps) {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Show button after a delay to avoid CLS
    const timer = setTimeout(() => setIsVisible(true), 2000);
    return () => clearTimeout(timer);
  }, []);

  const openCalendly = () => {
    // Always use direct link for simplicity
    window.open('https://calendly.com/advisync/15min', '_blank');
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "fixed bottom-6 right-6 z-50",
        "md:bottom-8 md:right-8",
        className
      )}
    >
      <motion.button
        onClick={openCalendly}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        className={cn(
          "group relative flex items-center gap-3 px-4 py-3 md:px-6 md:py-4",
          "bg-accent-secondary text-bg-primary rounded-full shadow-lg hover:shadow-xl",
          "transition-all duration-300 hover:bg-accent-secondary/90",
          "border border-accent-secondary/20 hover:shadow-glow-gold"
        )}
        aria-label="Book a consultation"
      >
        <Calendar className="w-5 h-5 md:w-6 md:h-6" />
        <span className="hidden sm:block font-medium text-sm md:text-base">
          Book Call
        </span>

        {/* Pulse animation */}
        <div className="absolute inset-0 rounded-full bg-accent-secondary/20 animate-ping" />
      </motion.button>
    </motion.div>
  );
}
