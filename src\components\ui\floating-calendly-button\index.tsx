'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Calendar, X } from 'lucide-react';
import { contactInfo } from '@/config/contact';
import { cn } from '@/lib/utils';

interface FloatingCalendlyButtonProps {
  url?: string;
  position?: 'bottom-right' | 'bottom-left';
  label?: string;
  className?: string;
}

export function FloatingCalendlyButton({
  url = contactInfo.calendly,
  position = 'bottom-right',
  label = 'Book a Call',
  className
}: FloatingCalendlyButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  // Show button after scrolling down a bit
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsVisible(scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Position classes
  const positionClasses = {
    'bottom-right': 'right-4 sm:right-6 bottom-4 sm:bottom-6',
    'bottom-left': 'left-4 sm:left-6 bottom-4 sm:bottom-6',
  };

  return (
    <>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={cn(
              "fixed z-50 flex flex-col items-end",
              positionClasses[position],
              className
            )}
          >
            {/* Popup Calendly Widget */}
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: 10 }}
                  className="bg-background/30 backdrop-blur-sm border border-primary/20 rounded-xl shadow-xl shadow-primary/20 mb-4 w-[350px] sm:w-[400px] overflow-hidden"
                >
                  <div className="flex items-center justify-between p-4 border-b border-primary/20">
                    <h3 className="font-medium text-white">Schedule a Meeting</h3>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-1 rounded-full hover:bg-primary/20 text-white/60 hover:text-primary transition-colors"
                      aria-label="Close calendar"
                    >
                      <X size={18} />
                    </button>
                  </div>
                  <div className="h-[500px]">
                    <iframe
                      src={url}
                      width="100%"
                      height="100%"
                      frameBorder="0"
                      title="Calendly Scheduling"
                      className="w-full h-full"
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Button */}
            <motion.button
              onClick={() => setIsOpen(!isOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "flex items-center gap-2 px-4 py-3 rounded-full bg-primary text-white shadow-lg shadow-primary/40 hover:shadow-xl hover:shadow-primary/50 transition-all border border-primary/30",
                isOpen && "bg-primary/90"
              )}
            >
              {isOpen ? (
                <>
                  <X size={18} />
                  <span>Close</span>
                </>
              ) : (
                <>
                  <Calendar size={18} />
                  <span>{label}</span>
                </>
              )}
              <div className="absolute inset-0 rounded-full bg-primary/20 blur-md -z-10"></div>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
