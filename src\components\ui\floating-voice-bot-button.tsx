'use client';

import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>c<PERSON><PERSON>, <PERSON> } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn } from '@/lib/utils';

interface FloatingVoiceBotButtonProps {
  className?: string;
}

export function FloatingVoiceBotButton({ className }: FloatingVoiceBotButtonProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isActive, setIsActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
    // Show button after a delay to avoid CLS
    const timer = setTimeout(() => setIsVisible(true), 3000);
    return () => clearTimeout(timer);
  }, []);

  const handleVoiceBotClick = async () => {
    if (isActive) {
      // Stop voice bot
      setIsActive(false);
      return;
    }

    setIsLoading(true);

    try {
      // Call the webhook to initiate the AI assistant call
      const response = await fetch('https://n8n.tmai.com.au/webhook/twilio-retell-handler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start_demo_call',
          source: 'website_floating_button',
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        console.log('AI Assistant call initiated successfully');
        setIsActive(true);

        // Auto-stop after 60 seconds as per requirements
        setTimeout(() => {
          setIsActive(false);
        }, 60000);
      } else {
        console.error('Failed to initiate AI Assistant call');
        // Fallback to demo page
        window.open('/demo', '_blank');
      }
    } catch (error) {
      console.error('Error initiating AI Assistant call:', error);
      // Fallback to demo page
      window.open('/demo', '_blank');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isMounted || !isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "fixed bottom-6 left-6 z-50",
        "md:bottom-8 md:left-8",
        className
      )}
    >
      <motion.button
        onClick={handleVoiceBotClick}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        disabled={isLoading}
        className={cn(
          "group relative flex items-center gap-3 px-5 py-4 md:px-7 md:py-5",
          "bg-gradient-to-r from-accent-secondary to-accent-secondary/90 text-bg-primary rounded-2xl shadow-xl hover:shadow-2xl",
          "transition-all duration-500 hover:from-accent-secondary/95 hover:to-accent-secondary/85",
          "border-2 border-accent-secondary/30 hover:border-accent-secondary/50",
          "backdrop-blur-sm hover:backdrop-blur-md",
          isActive
            ? "from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700"
            : "",
          isLoading && "opacity-50 cursor-not-allowed"
        )}
        aria-label={isActive ? "Stop voice bot" : "Get instant quote from AI assistant"}
      >
        {isLoading ? (
          <div className="w-5 h-5 md:w-6 md:h-6 animate-spin rounded-full border-2 border-white border-t-transparent" />
        ) : isActive ? (
          <MicOff className="w-5 h-5 md:w-6 md:h-6" />
        ) : (
          <div className="relative">
            <Bot className="w-6 h-6 md:w-7 md:h-7 drop-shadow-sm" />
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse border-2 border-white"></div>
          </div>
        )}

        <span className="hidden sm:block font-bold text-sm md:text-base tracking-wide drop-shadow-sm">
          {isLoading ? 'Connecting...' : isActive ? 'End Call' : 'Get Instant Quote'}
        </span>

        {/* Enhanced glow effect */}
        <div className="absolute inset-0 rounded-2xl bg-accent-secondary/20 blur-xl -z-10 group-hover:bg-accent-secondary/30 transition-all duration-500"></div>

        {/* Pulse animation when active */}
        {isActive && (
          <div className="absolute inset-0 rounded-2xl bg-red-500/20 animate-ping" />
        )}

        {/* Enhanced pulse when inactive */}
        {!isActive && !isLoading && (
          <div className="absolute inset-0 rounded-2xl bg-accent-secondary/15 animate-ping"></div>
        )}

        {/* Shimmer effect */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 group-hover:animate-shimmer"></div>
      </motion.button>
    </motion.div>
  );
}
