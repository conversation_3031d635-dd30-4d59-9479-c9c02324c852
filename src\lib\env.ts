import { z } from 'zod';

// Create a more flexible environment schema for production
const envSchema = z.object({
  // Base URLs
  NEXT_PUBLIC_API_URL: z.string().url('Invalid API URL').optional(),

  // N8N Configuration
  N8N_WEBHOOK_URL: z.string().url('Invalid N8N webhook URL').optional(),
  N8N_CONTACT_FORM_WEBHOOK: z.string().url('Invalid contact form webhook URL').optional(),
  N8N_NEWSLETTER_WEBHOOK: z.string().url('Invalid newsletter webhook URL').optional(),

  // Vapi Configuration for Voice Agent
  NEXT_PUBLIC_VAPI_PUBLIC_KEY: z.string().optional(),
  NEXT_PUBLIC_VAPI_ASSISTANT_ID: z.string().optional(),

  // Security
  WEBHOOK_SECRET_KEY: z.string().min(1, 'Webhook Secret Key is required').optional(),

  // Database - made optional
  DATABASE_URL: z.string().optional(),
});

export type Env = z.infer<typeof envSchema>;

function validateEnv(): Env {
  const isProd = process.env.NODE_ENV === 'production';
  
  try {
    // In production, provide fallbacks for optional environment variables
    if (isProd) {
      return {
        NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'https://advisync-agency-git-main-abdy-awales-projects.vercel.app/api',
        N8N_WEBHOOK_URL: process.env.N8N_WEBHOOK_URL,
        N8N_CONTACT_FORM_WEBHOOK: process.env.N8N_CONTACT_FORM_WEBHOOK,
        N8N_NEWSLETTER_WEBHOOK: process.env.N8N_NEWSLETTER_WEBHOOK,
        NEXT_PUBLIC_VAPI_PUBLIC_KEY: process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY,
        NEXT_PUBLIC_VAPI_ASSISTANT_ID: process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID,
        WEBHOOK_SECRET_KEY: process.env.WEBHOOK_SECRET_KEY || 'placeholder-key-for-production',
        DATABASE_URL: process.env.DATABASE_URL || 'placeholder-db-url',
      };
    }

    // In development, provide same fallbacks
    return {
      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
      N8N_WEBHOOK_URL: process.env.N8N_WEBHOOK_URL,
      N8N_CONTACT_FORM_WEBHOOK: process.env.N8N_CONTACT_FORM_WEBHOOK,
      N8N_NEWSLETTER_WEBHOOK: process.env.N8N_NEWSLETTER_WEBHOOK,
      NEXT_PUBLIC_VAPI_PUBLIC_KEY: process.env.NEXT_PUBLIC_VAPI_PUBLIC_KEY,
      NEXT_PUBLIC_VAPI_ASSISTANT_ID: process.env.NEXT_PUBLIC_VAPI_ASSISTANT_ID,
      WEBHOOK_SECRET_KEY: process.env.WEBHOOK_SECRET_KEY || 'dev-webhook-key',
      DATABASE_URL: process.env.DATABASE_URL || 'placeholder-db-url',
    };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map((err) => err.path.join('.'));
      throw new Error(
        `❌ Invalid environment variables: ${missingVars.join(', ')}\n` +
        'Please check your .env file'
      );
    }
    throw error;
  }
}

export const env = validateEnv(); 