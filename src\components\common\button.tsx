"use client";

import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-full text-sm font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-accent-primary focus-visible:shadow-md disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-accent-primary text-text-heading shadow-md hover:bg-accent-secondary hover:text-bg-primary focus:shadow-lg",
        destructive: "bg-error text-text-heading shadow-sm hover:bg-error/90",
        outline: "border border-border-hair bg-transparent shadow-sm hover:bg-surface-card hover:text-text-heading",
        secondary: "bg-accent-secondary text-bg-primary shadow-sm hover:bg-accent-primary hover:text-text-heading",
        ghost: "hover:bg-surface-card hover:text-text-heading",
        link: "text-accent-primary underline-offset-4 hover:underline hover:text-accent-secondary",
      },
      size: {
        default: "h-10 px-6 py-2",
        sm: "h-8 px-4 text-xs",
        lg: "h-12 px-8",
        xl: "h-14 px-10 sm:px-12 text-base",
        xxl: "h-16 px-12 sm:px-16 text-base sm:text-lg",
        icon: "h-10 w-10",
        responsive: "h-9 px-4 sm:px-6 py-2 text-sm sm:text-base",
        'responsive-lg': "h-12 sm:h-14 px-6 sm:px-8 py-2 sm:py-3 text-sm sm:text-base",
      },
      fullWidth: {
        true: "w-full",
      },
      rounded: {
        default: "rounded-md",
        full: "rounded-full",
      }
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      rounded: "default",
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean;
  fullWidth?: boolean;
  rounded?: "default" | "full";
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, fullWidth, rounded, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, fullWidth, rounded, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";

export { Button, buttonVariants }; 