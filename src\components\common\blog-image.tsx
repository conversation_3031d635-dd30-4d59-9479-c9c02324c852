'use client';

import Image from 'next/image';
import { useState } from 'react';
import { cn } from '@/lib/utils';

interface BlogImageProps {
  src: string;
  alt: string;
  priority?: boolean;
  className?: string;
  size?: 'small' | 'medium' | 'large';
  width?: number;
  height?: number;
}

const imageSizes = {
  small: { width: 400, height: 225 },    // 16:9 ratio
  medium: { width: 600, height: 338 },   // 16:9 ratio
  large: { width: 800, height: 450 }     // 16:9 ratio
};

export function BlogImage({ 
  src, 
  alt, 
  priority = false, 
  className = '', 
  size = 'medium',
  width: customWidth,
  height: customHeight 
}: BlogImageProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  
  // Always use predefined sizes, custom dimensions are optional override
  const dimensions = imageSizes[size];
  // Use explicit number values to avoid any type issues
  const finalWidth = typeof customWidth === 'number' ? customWidth : dimensions.width;
  const finalHeight = typeof customHeight === 'number' ? customHeight : dimensions.height;

  if (hasError) {
    return (
      <div 
        className={cn(
          "relative flex items-center justify-center bg-secondary/5",
          "border border-primary/10 rounded-lg",
          className
        )}
        style={{ width: finalWidth, height: finalHeight }}
      >
        <span className="text-sm text-foreground/60">Image not available</span>
      </div>
    );
  }

  return (
    <div className={cn("relative overflow-hidden rounded-lg", className)}>
      <Image
        src={src}
        alt={alt}
        width={finalWidth}
        height={finalHeight}
        priority={priority}
        className={cn(
          "duration-700 ease-in-out",
          isLoading ? "scale-110 blur-2xl grayscale" : "scale-100 blur-0 grayscale-0"
        )}
        onLoad={() => setIsLoading(false)}
        onError={() => setHasError(true)}
        quality={90}
      />
    </div>
  );
} 