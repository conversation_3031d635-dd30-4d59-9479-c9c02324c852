import { Metadata } from 'next';
import { Container } from '@/components/common/container';
import { PageHeader } from '@/components/common/page-header';
import { 
  BookOpen, 
  LifeBuoy, 
  MessageSquare, 
  Phone, 
  Mail, 
  FileQuestion,
  Lightbulb,
  FileText,
  Video,
  ArrowRight
} from 'lucide-react';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Support & Documentation | Advisync Solutions - Melbourne Business Automation',
  description: 'Access comprehensive support resources for Advisync Solutions services. Find documentation, guides, and expert help for business automation, web development, and digital transformation solutions.',
  keywords: [
    'business automation support',
    'digital transformation help',
    'Melbourne tech support',
    'automation documentation',
    'business process guides',
    'NDIS automation support',
    'web development help',
    'Melbourne digital solutions support',
    'workflow automation guides',
    'business efficiency support'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/support'
  },
  openGraph: {
    title: 'Support & Documentation | Advisync Solutions',
    description: 'Access comprehensive support resources for business automation and digital transformation solutions. Expert help available 24/7.',
    url: 'https://advisync.com.au/support',
    type: 'website',
    siteName: 'Advisync Solutions',
    locale: 'en_AU',
    images: [
      {
        url: '/images/og/support-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync Solutions Support & Documentation'
      }
    ]
  }
};

const resources = [
  {
    title: "Getting Started Guides",
    description: "Step-by-step guides to help you get started with our services",
    icon: BookOpen,
    href: "/docs/getting-started"
  },
  {
    title: "FAQs",
    description: "Find answers to commonly asked questions",
    icon: FileQuestion,
    href: "/docs/faqs"
  },
  {
    title: "Best Practices",
    description: "Learn how to get the most out of our services",
    icon: Lightbulb,
    href: "/docs/best-practices"
  },
  {
    title: "API Documentation",
    description: "Technical documentation for developers",
    icon: FileText,
    href: "/docs/api"
  },
  {
    title: "Video Tutorials",
    description: "Watch step-by-step video guides",
    icon: Video,
    href: "/docs/tutorials"
  }
];

const supportChannels = [
  {
    title: "Live Chat",
    description: "Chat with our support team in real-time",
    icon: MessageSquare,
    availability: "24/7 Support"
  },
  {
    title: "Phone Support",
    description: "Speak directly with our team",
    icon: Phone,
    availability: "Mon-Fri, 9am-5pm AEST",
    
  },
  {
    title: "Email Support",
    description: "Send us your questions",
    icon: Mail,
    availability: "Response within 24 hours",
    contact: "<EMAIL>"
  }
];

export default function SupportPage() {
  return (
    <main className="py-20">
      <Container>
        <PageHeader 
          title="Support & Documentation" 
          description="Get the help you need to succeed with our services"
        />

        {/* Support Channels */}
        <section className="mt-12">
          <h2 className="text-2xl font-bold mb-8">Contact Support</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {supportChannels.map((channel) => (
              <div 
                key={channel.title}
                className="bg-secondary/5 backdrop-blur-sm rounded-2xl p-6 border border-primary/10"
              >
                <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center mb-4">
                  <channel.icon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{channel.title}</h3>
                <p className="text-foreground/60 mb-4">{channel.description}</p>
                <div className="text-sm text-foreground/60">
                  <p className="font-medium text-primary">{channel.availability}</p>
                  {channel.contact && (
                    <p className="mt-1">{channel.contact}</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Help Resources */}
        <section className="mt-16">
          <h2 className="text-2xl font-bold mb-8">Help Resources</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {resources.map((resource) => (
              <Link
                key={resource.title}
                href={resource.href}
                className="group bg-secondary/5 backdrop-blur-sm rounded-2xl p-6 border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:translate-y-[-4px]"
              >
                <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center mb-4 group-hover:scale-110 transition-transform">
                  <resource.icon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2 group-hover:text-primary transition-colors">
                  {resource.title}
                </h3>
                <p className="text-foreground/60 mb-4">{resource.description}</p>
                <div className="flex items-center text-primary group-hover:translate-x-2 transition-transform">
                  <span className="text-sm font-medium">Learn more</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </div>
              </Link>
            ))}
          </div>
        </section>

        {/* Additional Support */}
        <section className="mt-16">
          <div className="bg-gradient-to-br from-primary/5 via-secondary/5 to-primary/5 rounded-2xl p-8 border border-primary/10">
            <h2 className="text-2xl font-bold mb-4">Need Additional Support?</h2>
            <p className="text-foreground/60 mb-6">
              Our team is here to help you succeed. For complex issues or custom requirements, 
              consider scheduling a consultation with our experts.
            </p>
            <Link
              href="/consultation"
              className="inline-flex items-center gap-2 px-6 py-3 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors group"
            >
              <span>Schedule Consultation</span>
              <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </section>
      </Container>
    </main>
  );
} 