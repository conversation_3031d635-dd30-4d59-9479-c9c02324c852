import { Metadata } from 'next';
import { Inter } from "next/font/google";
import { Navbar } from "@/components/layout/navbar";
import { Footer } from "@/components/layout/footer";
import { BackToTop } from '@/components/ui/back-to-top';
import { cn } from "@/lib/utils";
import { organizationLd } from "@/lib/json-ld";
import <PERSON>ript from 'next/script';
import { PreloaderProvider } from '@/contexts/PreloaderContext';
import { PreloaderWrapper } from '@/components/common/preloader-wrapper';
import { FloatingVoiceBotButton } from '@/components/ui/floating-voice-bot-button';
import { PerformanceMonitor } from '@/components/common/performance-monitor';
import { ClientOnly } from '@/components/common/client-only';
import './globals.css';

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  metadataBase: new URL('https://advisync.com.au'),
  title: {
    default: 'Advisync AI Solutions | AI Voice Agents & Workflow Automation',
    template: '%s | Advisync AI Solutions'
  },
  description: "Melbourne's trusted AI-automation agency specializing in voice agents and customer management automation for tradies (electricians, plumbers) and healthcare providers (chiropractors, physios).",
  keywords: [
    'ai voice agent australia',
    'website voice bot demo',
    'virtual receptionist melbourne',
    'n8n automation consultant',
    'business process automation melbourne',
    '24/7 customer service solution',
    'hands-free lead follow-up',
    'reduce call wait times',
    'automate data entry for smb',
    'ai workflow builder'
  ],
  authors: [{ name: 'Advisync AI Solutions' }],
  creator: 'Advisync AI Solutions',
  publisher: 'Advisync AI Solutions',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    type: 'website',
    locale: 'en_AU',
    url: 'https://advisync.com.au',
    title: 'Advisync AI Solutions | Digital Automation & Business Solutions',
    description: "Melbourne's trusted digital automation partner, helping small businesses transform their operations through intelligent automation and custom digital solutions.",
    siteName: 'Advisync AI Solutions',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI Solutions',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Advisync AI Solutions | Digital Automation & Business Solutions',
    description: "Melbourne's trusted digital automation partner, helping small businesses transform their operations through intelligent automation and custom digital solutions.",
    images: ['/og-image.jpg'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <head>
        <Script
          id="json-ld"
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationLd) }}
        />
      </head>
      <body
        className={cn("min-h-screen bg-background font-sans antialiased", inter.className)}
        suppressHydrationWarning={true}
      >
        <PreloaderProvider>
          <PreloaderWrapper />
          <div className="relative flex min-h-screen flex-col">
            <Navbar />
            <div className="flex-1">{children}</div>
            <Footer />
          </div>
          <BackToTop />
          <ClientOnly>
            <FloatingVoiceBotButton />
            <PerformanceMonitor />
          </ClientOnly>
        </PreloaderProvider>
      </body>
    </html>
  );
}