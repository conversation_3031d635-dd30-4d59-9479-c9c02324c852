import { <PERSON>ada<PERSON> } from "next";
import { ConsultationForm } from "@/components/sections/consultation-form";
import { PageHeader } from "@/components/common/page-header";
import { TestimonialsCarousel } from "@/components/sections/testimonials";
import { FAQ } from "@/components/sections/faq";
import { CheckCircle, Clock, Calendar, Users, BarChart, ArrowRight, FileText, Mail, Globe, Star, Shield, Zap } from "lucide-react";
import Link from "next/link";
import { Container } from "@/components/common/container";
import { CalendlyWidget } from "@/components/ui/calendly";

export const metadata: Metadata = {
  title: "Free Business Automation Consultation | Advisync Solutions",
  description: "Book a free consultation with Melbourne's automation experts. Our professionals will analyze your workflows and identify opportunities to optimize your business operations.",
  keywords: [
    'business automation consultation',
    'workflow optimization',
    'business process automation',
    'melbourne automation solutions',
    'document workflow automation',
    'email marketing automation',
    'appointment scheduling',
    'digital workflow optimization',
    'free consultation',
    'business efficiency',
    'process improvement',
    'workflow analysis',
    'business transformation'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/consultation'
  },
  openGraph: {
    title: "Free Business Automation Consultation | Advisync Solutions",
    description: "Book a free consultation with Melbourne's automation experts. Our professionals will analyze your workflows and identify opportunities to optimize your business operations.",
    url: 'https://advisync.com.au/consultation',
    type: 'website',
    images: [
      {
        url: '/images/og/consultation-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Book a Free Business Automation Consultation with Advisync Solutions'
      }
    ]
  }
};

// Add JSON-LD structured data
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Business Automation Consultation',
  description: "Professional consultation service for Melbourne businesses looking to optimize workflows and implement efficient automation solutions.",
  provider: {
    '@type': 'Organization',
    name: 'Advisync Solutions',
    url: 'https://advisync.com.au',
    logo: {
      '@type': 'ImageObject',
      url: 'https://advisync.com.au/images/advisync-logo.png'
    }
  },
  areaServed: {
    '@type': 'City',
    name: 'Melbourne',
    '@id': 'https://www.wikidata.org/wiki/Q3141'
  },
  serviceType: 'Business Process Optimization & Automation',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'AUD',
    availability: 'https://schema.org/InStock',
    validFrom: '2024-01-01'
  }
};

const consultationBenefits = [
  // Core Automation Services
  {
    title: "Basic Process Automation",
    description: "Connect 2-3 apps to eliminate manual data entry and save hours weekly. Results in 1-2 weeks.",
    icon: ArrowRight,
    complexity: "Simple",
    category: "Core Automation Services"
  },
  {
    title: "Email Marketing Automation",
    description: "Set up automatic follow-ups and newsletters to keep clients engaged with minimal effort. Ready within 1 week.",
    icon: Mail,
    complexity: "Simple",
    category: "Core Automation Services"
  },
  {
    title: "Form & Data Collection",
    description: "Create digital forms that automatically organize customer information and survey responses. Implemented in 1 week.",
    icon: FileText,
    complexity: "Simple",
    category: "Core Automation Services"
  },

  // Specialized Solutions
  {
    title: "Appointment Scheduling",
    description: "Implement a simple booking system with automatic reminders to reduce no-shows by 40%. See results in 1-2 weeks.",
    icon: Calendar,
    complexity: "Moderate",
    category: "Specialized Solutions"
  },
  {
    title: "Done-For-You Automation",
    description: "We handle all technical setup and management while you focus on your business. No technical knowledge required on your part. Begin seeing benefits in days.",
    icon: CheckCircle,
    complexity: "Simple",
    category: "Specialized Solutions"
  },
  {
    title: "Web Development & Design",
    description: "Custom website development with modern design and functionality tailored to your business needs.",
    icon: Globe,
    complexity: "Custom",
    category: "Specialized Solutions"
  },

  // Free Assessment
  {
    title: "Free Starter Assessment",
    description: "Receive a complimentary evaluation of your quick-win automation opportunities. Delivered within 48 hours.",
    icon: Clock,
    complexity: "Custom",
    category: "Free Assessment"
  }
];

const faqItems = [
  {
    question: "What happens during the consultation?",
    answer: "During our consultation, we'll discuss your organization's challenges, current processes, and goals. We'll identify potential automation opportunities and provide initial recommendations tailored to your specific needs."
  },
  {
    question: "Is the consultation really free?",
    answer: "Yes, the initial consultation is completely free with no obligations. We believe in providing value first and demonstrating our expertise before you make any decisions about working with us."
  },
  {
    question: "How should I prepare for the consultation?",
    answer: "To make the most of our time together, consider the processes that are currently manual, time-consuming, or error-prone. Think about your pain points and what you'd like to improve. No technical preparation is needed."
  },
  {
    question: "Who will I be speaking with?",
    answer: "You'll be speaking directly with one of our senior automation specialists who has extensive experience working with Melbourne businesses and service providers across various industries."
  },
  {
    question: "What happens after I submit my consultation request?",
    answer: "After receiving your request, our team will review your information and contact you within 24 hours to schedule a consultation at a time that works for you."
  },
  {
    question: "What types of automation solutions do you offer?",
    answer: "We specialize in business process automation, client management systems, appointment scheduling, document automation, workflow automation, AI solutions for inbound and outbound calls, and custom web development. We can help streamline your operations, improve client service, and increase efficiency."
  },
  {
    question: "Do you work with both small businesses and service providers?",
    answer: "Yes, we work with organizations of all sizes, from small local businesses to larger service providers. Our solutions are customized to your specific needs and scale with your growth."
  },
  {
    question: "How can automation help with compliance requirements?",
    answer: "Our automation solutions can help ensure your organization meets compliance requirements by standardizing processes, maintaining accurate records, and providing audit trails. This reduces manual errors and helps maintain consistent service quality."
  }
];

export default function ConsultationPage() {
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      {/* Simplified Header Section */}
      <PageHeader
        title="Free Consultation"
        className="pb-6 md:pb-8 pt-16 md:pt-20"
      />

      {/* Enhanced Hero Section */}
      <section className="bg-bg-primary py-12 md:py-20">
        <Container className="px-4 sm:px-6 md:px-8">
          <div className="text-center max-w-4xl mx-auto mb-12 md:mb-16">
            <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-text-heading">
              Transform Your Business with a
              <span className="text-accent-secondary"> Free Consultation</span>
            </h1>
            <p className="text-lg md:text-xl text-text-body mb-8 max-w-3xl mx-auto leading-relaxed">
              Whether you're a <span className="text-accent-secondary font-medium">tradie</span> (electrician, plumber, builder)
              or <span className="text-accent-secondary font-medium"> healthcare provider</span> (chiropractor, physio, dentist),
              our automation experts will analyze your workflows and identify specific opportunities to save time, reduce costs, and improve customer experience.
            </p>

            {/* Key Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              <div className="bg-surface-card border border-border-hair rounded-xl p-6">
                <div className="w-12 h-12 bg-accent-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Clock className="w-6 h-6 text-accent-primary" />
                </div>
                <h3 className="text-lg font-semibold text-text-heading mb-2">Quick Response</h3>
                <p className="text-text-body text-sm">Get a response within 24 hours</p>
              </div>

              <div className="bg-surface-card border border-border-hair rounded-xl p-6">
                <div className="w-12 h-12 bg-accent-secondary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Users className="w-6 h-6 text-accent-secondary" />
                </div>
                <h3 className="text-lg font-semibold text-text-heading mb-2">Expert Consultation</h3>
                <p className="text-text-body text-sm">Speak with our automation specialists</p>
              </div>

              <div className="bg-surface-card border border-border-hair rounded-xl p-6">
                <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <Shield className="w-6 h-6 text-success" />
                </div>
                <h3 className="text-lg font-semibold text-text-heading mb-2">No Obligation</h3>
                <p className="text-text-body text-sm">Free consultation with no commitments</p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Consultation Form Section */}
      <section className="bg-surface-card py-12 md:py-16">
        <Container className="px-4 sm:px-6 md:px-8">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-2xl md:text-3xl font-bold mb-4 text-text-heading">
                Choose Your <span className="text-accent-secondary">Service</span>
              </h2>
              <p className="text-text-body">
                Select the service you're interested in and we'll customize our consultation to your specific needs.
              </p>
            </div>

            <ConsultationForm />
          </div>
        </Container>
      </section>

      {/* Services Section */}
      <section className="bg-bg-primary py-12 md:py-16">
        <Container className="px-4 sm:px-6 md:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-text-heading">
              Our <span className="text-accent-secondary">Automation Services</span>
            </h2>
            <p className="text-text-body max-w-2xl mx-auto">
              From simple process automation to comprehensive digital transformation, we offer solutions that grow with your business.
            </p>
          </div>

          {/* Group services by category */}
          {['Core Automation Services', 'Specialized Solutions', 'Free Assessment'].map((category, categoryIndex) => (
            <div key={categoryIndex} className="mb-12 md:mb-16">
              <h3 className="text-xl md:text-2xl font-semibold mb-6 md:mb-8 text-accent-primary text-center">{category}</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
                {consultationBenefits
                  .filter(benefit => benefit.category === category)
                  .map((benefit, index) => (
                    <div
                      key={index}
                      className="bg-surface-card border border-border-hair rounded-xl p-6 hover:border-accent-primary/30 transition-all duration-300 hover:translate-y-[-2px] group"
                    >
                      <div className="w-12 h-12 rounded-xl bg-accent-primary/10 flex items-center justify-center mb-4 group-hover:bg-accent-primary/20 transition-colors">
                        <benefit.icon className="w-6 h-6 text-accent-primary" />
                      </div>
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-lg font-semibold text-text-heading">{benefit.title}</h3>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          benefit.complexity === "Simple" ? "bg-success/20 text-success" :
                          benefit.complexity === "Moderate" ? "bg-accent-secondary/20 text-accent-secondary" :
                          benefit.complexity === "Custom" ? "bg-accent-primary/20 text-accent-primary" :
                          "bg-accent-primary/20 text-accent-primary"
                        }`}>
                          {benefit.complexity}
                        </span>
                      </div>
                      <p className="text-text-body text-sm leading-relaxed">{benefit.description}</p>
                    </div>
                  ))}
              </div>
            </div>
          ))}
        </Container>
      </section>

      {/* Growth Partnership Section */}
      <section className="bg-surface-card py-12 md:py-16">
        <Container className="px-4 sm:px-6 md:px-8">
          <div className="bg-accent-primary/5 border border-accent-primary/20 rounded-2xl p-8 md:p-12 text-center max-w-4xl mx-auto">
            <div className="w-16 h-16 bg-accent-secondary/10 rounded-2xl flex items-center justify-center mx-auto mb-6">
              <Star className="w-8 h-8 text-accent-secondary" />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-4 text-text-heading">
              Your Long-Term <span className="text-accent-secondary">Automation Partner</span>
            </h2>
            <p className="text-lg text-text-body max-w-3xl mx-auto mb-8 leading-relaxed">
              As your automation partner, we grow with you - starting with simple solutions and expanding as your needs evolve. Begin with quick wins that deliver immediate value, then build on your success with more advanced automation as your business grows.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-success/10 rounded-xl flex items-center justify-center mb-3">
                  <Zap className="w-6 h-6 text-success" />
                </div>
                <h3 className="font-semibold text-text-heading mb-2">Start Small</h3>
                <p className="text-text-body text-sm">Scale gradually with proven solutions</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-accent-primary/10 rounded-xl flex items-center justify-center mb-3">
                  <BarChart className="w-6 h-6 text-accent-primary" />
                </div>
                <h3 className="font-semibold text-text-heading mb-2">Build Success</h3>
                <p className="text-text-body text-sm">Expand on early wins and achievements</p>
              </div>
              <div className="flex flex-col items-center text-center">
                <div className="w-12 h-12 bg-accent-secondary/10 rounded-xl flex items-center justify-center mb-3">
                  <ArrowRight className="w-6 h-6 text-accent-secondary" />
                </div>
                <h3 className="font-semibold text-text-heading mb-2">Evolve Together</h3>
                <p className="text-text-body text-sm">Flexible solutions that grow with you</p>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* General FAQ Section */}
      <FAQ />

      {/* Testimonials Section */}
      <TestimonialsCarousel />
    </main>
  );
}