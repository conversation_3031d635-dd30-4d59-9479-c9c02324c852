'use client';

import { usePreloader } from '@/contexts/PreloaderContext';
import { Preloader } from './preloader';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';

export function PreloaderWrapper() {
  const { isLoading, hidePreloader } = usePreloader();
  const [isBrowser, setIsBrowser] = useState(false);
  const pathname = usePathname();
  
  // Only show preloader on home page
  const isHomePage = pathname === '/';
  
  // Set isBrowser to true once component mounts
  useEffect(() => {
    setIsBrowser(true);
  }, []);
  
  // Don't render anything during server-side rendering or if not on home page
  if (!isBrowser || !isHomePage) {
    return null;
  }
  
  return (
    <Preloader 
      isLoading={isLoading} 
      onLoadingComplete={hidePreloader}
      minimumLoadingTime={4000} // 4 seconds minimum display time
    />
  );
} 