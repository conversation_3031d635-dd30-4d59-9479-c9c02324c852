'use client';

import { useState } from 'react';
import { Send, Loader2, CheckCircle, AlertCircle, Mail } from 'lucide-react';

type FormStatus = 'idle' | 'loading' | 'success' | 'error';

interface NewsletterFormProps {
  title?: string;
  description?: string;
  className?: string;
}

export function NewsletterForm({
  title = "Stay Updated with Melbourne Tech Trends",
  description = "Get the latest digital transformation tips and insights for Melbourne businesses delivered directly to your inbox.",
  className = "",
}: NewsletterFormProps) {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<FormStatus>('idle');
  const [message, setMessage] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Form submitted with email:", email); // Debug log
    
    if (!email || !email.includes('@')) {
      setStatus('error');
      setMessage('Please enter a valid email address');
      return;
    }
    
    try {
      setStatus('loading');
      console.log("Sending request to /api/subscribe"); // Debug log
      
      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });
      
      console.log("Response received:", response.status); // Debug log
      const data = await response.json();
      console.log("Response data:", data); // Debug log
      
      if (response.ok) {
        setStatus('success');
        setMessage(data.message);
        setEmail('');
      } else {
        setStatus('error');
        setMessage(data.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      setStatus('error');
      setMessage('Something went wrong. Please try again.');
    }
  };

  return (
    <div className={`relative z-10 ${className}`}>
      <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl -z-10" />
      <div className="text-center p-8 md:p-16 relative z-10">
        {status === 'success' ? (
          <div className="flex flex-col items-center justify-center">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
            <h2 className="text-2xl md:text-3xl font-bold mb-4">
              Subscription Successful!
            </h2>
            <p className="text-lg text-foreground/60 mb-4 max-w-2xl mx-auto">
              {message}
            </p>
            <p className="text-sm text-foreground/60 max-w-2xl mx-auto">
              Please check your inbox for a confirmation email. If you don't see it, please check your spam folder.
            </p>
          </div>
        ) : (
          <>
            <h2 className="text-2xl md:text-3xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
              {title}
            </h2>
            <p className="text-lg md:text-xl text-foreground/60 mb-8 max-w-2xl mx-auto">
              {description}
            </p>
            
            <form onSubmit={handleSubmit} className="flex flex-col md:flex-row gap-4 justify-center max-w-md mx-auto">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                disabled={status === 'loading'}
                className="flex-1 px-6 py-3 bg-background border border-primary/10 rounded-full focus:outline-none focus:border-primary/20 transition-colors disabled:opacity-70 z-10"
              />
              <button 
                type="submit" 
                disabled={status === 'loading'}
                className="relative px-8 py-3 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors disabled:opacity-70 flex items-center justify-center gap-2 cursor-pointer z-10"
                onClick={(e) => {
                  console.log("Button clicked"); // Debug log
                  if (status !== 'loading') {
                    e.currentTarget.form?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                  }
                }}
              >
                {status === 'loading' ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    <span>Subscribing...</span>
                  </>
                ) : (
                  <>
                    <Mail className="w-4 h-4" />
                    <span>Subscribe</span>
                  </>
                )}
              </button>
            </form>
            
            {message && status === 'error' && (
              <div className="mt-4 text-sm flex items-center justify-center gap-2 text-red-500">
                <AlertCircle className="w-4 h-4" />
                <span>{message}</span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
} 