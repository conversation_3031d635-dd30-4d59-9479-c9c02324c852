import type { Metadata } from 'next';
import { Container } from '@/components/common/container';
import { VoiceBot } from '@/components/ui/voice-bot';
import { <PERSON><PERSON><PERSON><PERSON>, Bot, Mic, Volume2, Zap } from 'lucide-react';
import Link from 'next/link';
import { generateWebPageLd } from '@/lib/json-ld';

export const metadata: Metadata = {
  title: 'AI Voice Bot Demo | Advisync AI',
  description: 'Experience our AI voice agent in action. Talk to our virtual receptionist and see how it can help your business provide 24/7 customer service.',
  keywords: [
    'website voice bot demo',
    'ai voice agent australia',
    'virtual receptionist melbourne',
    '24/7 customer service solution',
    'hands-free lead follow-up'
  ],
  robots: {
    index: false,
    follow: true
  }
};

// JSON-LD structured data
const jsonLd = generateWebPageLd(
  'AI Voice Bot Demo | Advisync AI',
  'Experience our AI voice agent in action. Talk to our virtual receptionist and see how it can help your business provide 24/7 customer service.',
  'https://advisync.com.au/demo'
);

export default function DemoPage() {
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <Container className="py-12 md:py-16">
        <div className="mb-8">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-6"
          >
            <ArrowLeft className="w-4 h-4" />
            <span>Back to Home</span>
          </Link>

          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            AI Voice Agent Demo
          </h1>
          <p className="text-lg text-foreground/70 max-w-3xl">
            Experience our AI voice assistant in action. This demo showcases how your business can provide 24/7 customer service with natural, human-like conversations.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div className="bg-background/30 border border-primary/20 rounded-2xl p-8 shadow-lg backdrop-blur-sm">
            <div className="flex justify-center mb-8">
              <VoiceBot autoplay={true} maxDuration={60} />
            </div>

            <div className="space-y-4 mt-8">
              <h2 className="text-xl font-semibold flex items-center gap-2 text-white">
                <Volume2 className="w-5 h-5 text-primary" />
                <span>How to Use This Demo</span>
              </h2>
              <ul className="space-y-3">
                <li className="flex items-start gap-3">
                  <span className="bg-primary/20 text-primary border border-primary/30 rounded-full w-7 h-7 flex items-center justify-center flex-shrink-0 mt-0.5 shadow-glow-sm">1</span>
                  <span className="text-white/90">Click the microphone button to start the conversation</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-primary/20 text-primary border border-primary/30 rounded-full w-7 h-7 flex items-center justify-center flex-shrink-0 mt-0.5 shadow-glow-sm">2</span>
                  <span className="text-white/90">Speak naturally as if talking to a human receptionist</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-primary/20 text-primary border border-primary/30 rounded-full w-7 h-7 flex items-center justify-center flex-shrink-0 mt-0.5 shadow-glow-sm">3</span>
                  <span className="text-white/90">Try asking about services, pricing, or booking a consultation</span>
                </li>
                <li className="flex items-start gap-3">
                  <span className="bg-primary/20 text-primary border border-primary/30 rounded-full w-7 h-7 flex items-center justify-center flex-shrink-0 mt-0.5 shadow-glow-sm">4</span>
                  <span className="text-white/90">The demo is limited to 60 seconds (click again to restart)</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="space-y-8">
            <div className="bg-background/30 border border-primary/20 rounded-2xl p-6 shadow-lg backdrop-blur-sm">
              <h2 className="text-xl font-semibold mb-4 flex items-center gap-2 text-white">
                <Bot className="w-5 h-5 text-secondary" />
                <span>Voice Agent Benefits</span>
              </h2>
              <ul className="space-y-4">
                <li className="flex items-start gap-3">
                  <div className="p-2 rounded-lg bg-secondary/20 border border-secondary/30 shadow-glow-sm">
                    <Zap className="w-5 h-5 text-secondary" />
                  </div>
                  <div>
                    <p className="font-medium text-white">24/7 Customer Service</p>
                    <p className="text-sm text-white/70">Never miss a customer inquiry, even outside business hours</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="p-2 rounded-lg bg-secondary/20 border border-secondary/30 shadow-glow-sm">
                    <Zap className="w-5 h-5 text-secondary" />
                  </div>
                  <div>
                    <p className="font-medium text-white">Reduce Call Wait Times</p>
                    <p className="text-sm text-white/70">Handle multiple conversations simultaneously with no hold times</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="p-2 rounded-lg bg-secondary/20 border border-secondary/30 shadow-glow-sm">
                    <Zap className="w-5 h-5 text-secondary" />
                  </div>
                  <div>
                    <p className="font-medium text-white">Hands-Free Lead Follow-Up</p>
                    <p className="text-sm text-white/70">Automatically qualify leads and schedule appointments</p>
                  </div>
                </li>
                <li className="flex items-start gap-3">
                  <div className="p-2 rounded-lg bg-secondary/20 border border-secondary/30 shadow-glow-sm">
                    <Zap className="w-5 h-5 text-secondary" />
                  </div>
                  <div>
                    <p className="font-medium text-white">Natural Conversations</p>
                    <p className="text-sm text-white/70">Human-like interactions that build customer trust</p>
                  </div>
                </li>
              </ul>
            </div>

            <div className="bg-gradient-to-r from-primary/20 to-secondary/20 rounded-2xl p-6 shadow-lg border border-white/10 backdrop-blur-sm">
              <h2 className="text-xl font-semibold mb-4 text-white">Ready to implement this on your website?</h2>
              <p className="mb-4 text-white/80">
                Our AI voice agents can be customized for your specific business needs and integrated with your existing systems.
              </p>
              <div className="flex flex-wrap gap-4 mt-6">
                <Link
                  href="/services#voice-agents"
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors border border-primary/30 shadow-glow-sm"
                >
                  Learn More
                </Link>
                <Link
                  href="/consultation"
                  className="px-4 py-2 bg-secondary text-white rounded-lg hover:bg-secondary/90 transition-colors border border-secondary/30 shadow-glow-sm"
                >
                  Book a Consultation
                </Link>
              </div>
            </div>
          </div>
        </div>
      </Container>
    </main>
  );
}
