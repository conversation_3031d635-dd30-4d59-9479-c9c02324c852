# N8N Integration Guide

This document explains how to set up N8N workflows to handle form submissions and voice agent interactions for your Advisync website.

## Overview

The website is configured to send all form data to N8N webhooks instead of using email services directly. This gives you complete control over how forms are processed, notifications are sent, and data is stored.

## Environment Variables Required

Copy the `.env.example` file to `.env.local` and configure these variables:

### N8N Webhooks
```bash
N8N_WEBHOOK_URL=https://your-n8n-instance.com/webhook/main
N8N_CONTACT_FORM_WEBHOOK=https://your-n8n-instance.com/webhook/contact-form
N8N_NEWSLETTER_WEBHOOK=https://your-n8n-instance.com/webhook/newsletter
```

### Vapi Voice Agent
```bash
NEXT_PUBLIC_VAPI_PUBLIC_KEY=your_vapi_public_key_here
NEXT_PUBLIC_VAPI_ASSISTANT_ID=your_vapi_assistant_id_here
```

## Form Data Structure

### Contact Form (`/api/contact`)
```json
{
  "type": "contact_form",
  "name": "<PERSON>",
  "email": "<EMAIL>",
  "company": "Example Corp",
  "phone": "+61400000000",
  "teamSize": "5-10",
  "contactPreference": "email",
  "service": "automation",
  "message": "Interested in automation solutions",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "source": "advisync-website"
}
```

### Newsletter Subscription (`/api/newsletter`, `/api/subscribe`)
```json
{
  "type": "newsletter_subscription",
  "email": "<EMAIL>",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "source": "advisync-website"
}
```

### Consultation Request (`/api/save-consultation`)
```json
{
  "type": "consultation_request",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+61400000000",
  "company": "Example Corp",
  "teamSize": "5-10",
  "service": "free-starter-assessment",
  "serviceName": "Free Starter Assessment",
  "serviceCategory": "assessment",
  "time": "Morning (9am-12pm)",
  "message": "Looking forward to the assessment",
  "submitDate": "2024-01-01T00:00:00.000Z",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "source": "advisync-website"
}
```

## N8N Workflow Suggestions

### 1. Contact Form Workflow
- **Trigger**: Webhook (N8N_CONTACT_FORM_WEBHOOK)
- **Actions**:
  - Send notification email to admin
  - Send confirmation email to customer
  - Save to CRM/database
  - Create task in project management tool
  - Send Slack notification

### 2. Newsletter Workflow
- **Trigger**: Webhook (N8N_NEWSLETTER_WEBHOOK)
- **Actions**:
  - Add to email marketing platform (Mailchimp, ConvertKit, etc.)
  - Send welcome email
  - Update subscriber count
  - Log to analytics

### 3. Voice Agent Integration
- **Trigger**: Vapi webhook (configure in Vapi dashboard)
- **Actions**:
  - Process voice conversation data
  - Extract lead information
  - Send follow-up emails
  - Create calendar appointments
  - Update CRM with call notes

## Voice Agent Setup

1. **Create Vapi Account**: Sign up at [vapi.ai](https://vapi.ai)
2. **Create Assistant**: Configure your voice agent with business context
3. **Get Credentials**: Copy your public key and assistant ID
4. **Configure Webhooks**: Set up N8N webhooks in Vapi dashboard
5. **Test Integration**: Use the floating voice button on your website

## Testing

The website will work without N8N configured - forms will simply skip the webhook calls and return success. This ensures user experience isn't affected during setup.

To test:
1. Submit a form on your website
2. Check N8N execution logs
3. Verify webhook data structure
4. Test voice agent functionality

## Deployment

When deploying to production (Vercel), make sure to:
1. Set all environment variables in Vercel dashboard
2. Test webhooks with production URLs
3. Configure Vapi with production domain
4. Monitor N8N workflow executions

## Support

If you need help setting up the N8N workflows or Vapi integration, the code is designed to be flexible and easy to modify for your specific needs.
