const fs = require('fs');
const path = require('path');
const https = require('https');
const sharp = require('sharp');

const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY;
const CASE_STUDIES_IMAGE_DIRECTORY = path.join(process.cwd(), 'public/images/case-studies');

const caseStudyImages = [
  {
    query: 'electrician emergency call system AI voice agent',
    filename: 'electrician-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'Electrician AI voice agent emergency call system'
  },
  {
    query: 'physiotherapy clinic AI receptionist appointment booking',
    filename: 'physiotherapy-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'Physiotherapy clinic AI receptionist system'
  },
  {
    query: 'plumber quote follow-up workflow automation system',
    filename: 'plumber-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'Plumber quote follow-up automation system'
  },
  {
    query: 'NDIS support coordination participant management system',
    filename: 'ndis-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'NDIS participant management automation'
  },
  {
    query: 'chiropractic treatment plan patient recall system',
    filename: 'chiropractic-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'Chiropractic patient recall automation system'
  },
  {
    query: 'dental practice patient communication automation',
    filename: 'dental-automation.jpg',
    width: 1200,
    height: 900,
    alt: 'Dental practice patient communication automation'
  }
];

async function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        const writeStream = fs.createWriteStream(filepath);
        response.pipe(writeStream);
        writeStream.on('finish', () => {
          writeStream.close();
          resolve();
        });
      } else {
        reject(new Error(`Failed to download image: ${response.statusCode}`));
      }
    }).on('error', reject);
  });
}

async function optimizeImage(filepath, width, height) {
  const options = {
    fit: 'cover',
    position: 'center',
    width: width,
    height: height
  };

  try {
    // Create optimized JPEG
    await sharp(filepath)
      .resize(options)
      .jpeg({
        quality: 85,
        progressive: true,
        chromaSubsampling: '4:4:4'
      })
      .toFile(`${filepath}.optimized`);

    fs.unlinkSync(filepath);
    fs.renameSync(`${filepath}.optimized`, filepath);

    // Create WebP version
    await sharp(filepath)
      .resize(options)
      .webp({
        quality: 85,
        effort: 6
      })
      .toFile(filepath.replace('.jpg', '.webp'));

    console.log(`Successfully optimized ${filepath}`);
  } catch (error) {
    console.error(`Error optimizing ${filepath}:`, error);
    throw error;
  }
}

async function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }
}

async function processImages(images, directory) {
  for (const image of images) {
    const filepath = path.join(directory, image.filename);
    
    try {
      // Download image from Unsplash
      const response = await fetch(
        `https://api.unsplash.com/photos/random?query=${encodeURIComponent(image.query)}&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch image URL: ${response.statusText}`);
      }

      const data = await response.json();
      const imageUrl = data.urls.raw;

      console.log(`Downloading ${image.filename}...`);
      await downloadImage(imageUrl, filepath);

      console.log(`Optimizing ${image.filename}...`);
      await optimizeImage(filepath, image.width, image.height);

      console.log(`Successfully processed ${image.filename}`);
    } catch (error) {
      console.error(`Error processing ${image.filename}:`, error);
    }
  }
}

async function main() {
  if (!UNSPLASH_ACCESS_KEY) {
    console.error('Please set UNSPLASH_ACCESS_KEY environment variable');
    process.exit(1);
  }

  // Ensure directory exists
  await ensureDirectoryExists(CASE_STUDIES_IMAGE_DIRECTORY);

  // Process case study images
  console.log('\nProcessing case study images...');
  await processImages(caseStudyImages, CASE_STUDIES_IMAGE_DIRECTORY);

  console.log('\nAll case study images have been processed successfully!');
}

main().catch(console.error); 