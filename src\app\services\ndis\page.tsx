import { Metadata } from 'next';
import { Container } from '@/components/common/container';
import { PageHeader } from '@/components/common/page-header';
import {
  Users,
  ClipboardCheck,
  Calendar,
  Clock,
  FileText,
  CheckCircle,
  ArrowRight,
  BarChart2
} from 'lucide-react';
import Link from 'next/link';
import { ndisFaqs } from '@/components/sections/faq/ndis-faqs';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/common/accordion";

export const metadata: Metadata = {
  title: 'Simple NDIS Provider Solutions | Melbourne',
  description: 'Basic automation solutions for Melbourne NDIS providers to reduce paperwork and streamline client management using easy-to-implement tools.',
  keywords: [
    'NDIS provider automation',
    'simple NDIS solutions',
    'NDIS paperwork reduction',
    'basic NDIS automation',
    'NDIS client management',
    'NDIS appointment scheduling',
    'NDIS documentation templates',
    'Melbourne NDIS technology',
    'disability service automation',
    'NDIS compliance tools',
    'NDIS time tracking',
    'NDIS reporting tools',
    'Melbourne NDIS digital solutions'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/services/ndis'
  },
  openGraph: {
    title: 'Simple NDIS Provider Solutions | Melbourne',
    description: 'Basic automation solutions for Melbourne NDIS providers to reduce paperwork and streamline client management using easy-to-implement tools.',
    url: 'https://advisync.com.au/services/ndis',
    type: 'website',
    images: [
      {
        url: '/images/og/ndis-services-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI Solutions - Simple NDIS Provider Solutions'
      }
    ]
  }
};

// Add JSON-LD structured data
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: 'Simple NDIS Provider Solutions',
  description: 'Basic automation solutions for Melbourne NDIS providers to reduce paperwork and streamline client management using easy-to-implement tools.',
  provider: {
    '@type': 'Organization',
    name: 'Advisync Solutions',
    url: 'https://advisync.com.au',
    logo: {
      '@type': 'ImageObject',
      url: 'https://advisync.com.au/images/advisync-logo.png'
    }
  },
  areaServed: {
    '@type': 'City',
    name: 'Melbourne',
    '@id': 'https://www.wikidata.org/wiki/Q3141'
  },
  serviceType: 'NDIS Provider Automation',
  offers: {
    '@type': 'Offer',
    priceCurrency: 'AUD',
    availability: 'https://schema.org/InStock',
    validFrom: '2024-01-01'
  }
};

const features = [
  {
    icon: Users,
    title: 'Client Management',
    description: 'Basic client information tracking and service record management'
  },
  {
    icon: Calendar,
    title: 'Appointment Scheduling',
    description: 'Simple calendar integration for client appointments'
  },
  {
    icon: FileText,
    title: 'Document Templates',
    description: 'Pre-built templates for common NDIS documentation'
  },
  {
    icon: ClipboardCheck,
    title: 'Basic Compliance',
    description: 'Simple checklists for NDIS compliance requirements'
  },
  {
    icon: Clock,
    title: 'Time Tracking',
    description: 'Basic time tracking for service delivery'
  },
  {
    icon: BarChart2,
    title: 'Simple Reporting',
    description: 'Essential reports for NDIS claiming and service tracking'
  }
];

const benefits = [
  'Reduce paperwork with simple digital forms',
  'Keep track of client information in one place',
  'Save time with basic appointment scheduling',
  'Simplify NDIS documentation with templates'
];

const processSteps = [
  {
    title: 'Quick Assessment',
    description: 'We identify your most pressing administrative challenges.'
  },
  {
    title: 'Simple Setup',
    description: 'We set up basic automation tools that are easy to use.'
  },
  {
    title: 'Implementation',
    description: 'We implement straightforward solutions using platforms like n8n, Make.com, or GHL.'
  },
  {
    title: 'Basic Training',
    description: 'We show your team how to use the simple automation tools.'
  }
];

const caseStudies = [
  {
    title: 'Small NDIS Provider',
    result: '50% reduction in paperwork',
    description: 'A small Melbourne-based NDIS provider was overwhelmed with paperwork and basic administrative tasks. We implemented a simple automation solution that helped them digitize their forms and client records, resulting in a 50% reduction in paperwork and much more efficient operations.'
  },
  {
    title: 'Solo NDIS Practitioner',
    result: '10 hours saved per week on admin',
    description: 'A solo NDIS practitioner was spending too much time on administrative tasks instead of client care. We set up basic automation tools for appointment scheduling and documentation, saving them 10 hours per week that could be redirected to providing better service to clients.'
  }
];

export default function NDISServicesPage() {
  return (
    <main className="pt-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />

      <PageHeader
        title="Simple NDIS Provider Solutions"
        description="Basic automation tools to help NDIS providers reduce paperwork and streamline client management."
      />

      {/* Features Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Easy-to-Use NDIS Solutions</h2>
            <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
              Our simple automation tools help NDIS providers spend less time on paperwork and more time on client care.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="p-6 bg-secondary/5 border border-border/30 rounded-xl hover:border-primary/20 transition-colors"
              >
                <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="w-6 h-6 text-primary" />
                </div>
                <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
                <p className="text-foreground/60">{feature.description}</p>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-secondary/5">
        <Container>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Benefits for NDIS Providers</h2>
              <p className="text-xl text-foreground/60 mb-8">
                Our simple solutions deliver practical benefits for NDIS providers of all sizes.
              </p>

              <ul className="space-y-4">
                {benefits.map((benefit, index) => (
                  <li key={index} className="flex items-start gap-3">
                    <CheckCircle className="w-6 h-6 text-accent-secondary flex-shrink-0 mt-0.5" />
                    <span className="text-lg">{benefit}</span>
                  </li>
                ))}
              </ul>

              <div className="mt-10">
                <Link
                  href="/consultation"
                  className="inline-flex items-center gap-2 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
                >
                  Book a Free Consultation
                  <ArrowRight className="w-4 h-4" />
                </Link>
              </div>
            </div>

            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-3xl transform rotate-3 scale-95 blur-xl opacity-30" />
              <div className="relative bg-background border border-border/30 rounded-3xl p-8 shadow-lg">
                <h3 className="text-2xl font-bold mb-4">NDIS Provider Feedback</h3>
                <p className="text-foreground/70 mb-6">
                  "As a small NDIS provider, I was drowning in paperwork. Advisync's simple automation tools have made a huge difference. I now spend more time with clients and less time on admin tasks."
                </p>
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium">Michael Chen</p>
                    <p className="text-sm text-foreground/60">NDIS Provider, Melbourne</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </Container>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Our Simple Process</h2>
            <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
              We make it easy to get started with basic automation for your NDIS practice.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <div
                key={index}
                className="p-6 bg-background border border-border/30 rounded-xl relative"
              >
                <div className="absolute -top-4 -left-4 w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center font-bold">
                  {index + 1}
                </div>
                <h3 className="text-xl font-semibold mb-3 mt-2">{step.title}</h3>
                <p className="text-foreground/60">{step.description}</p>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* Case Studies Section */}
      <section className="py-20 bg-secondary/5">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold mb-4">Success Stories</h2>
            <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
              See how our simple solutions have helped other NDIS providers.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {caseStudies.map((study, index) => (
              <div
                key={index}
                className="p-8 bg-background border border-border/30 rounded-xl"
              >
                <div className="mb-4 inline-block px-4 py-1 bg-primary/10 text-primary rounded-full">
                  Case Study {index + 1}
                </div>
                <h3 className="text-2xl font-bold mb-2">{study.title}</h3>
                <p className="text-primary font-semibold mb-4">{study.result}</p>
                <p className="text-foreground/70">{study.description}</p>
              </div>
            ))}
          </div>
        </Container>
      </section>

      {/* FAQ Section */}
      <section className="py-20">
        <Container>
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl font-bold text-center mb-8">
              Frequently Asked Questions
            </h2>
            <p className="text-foreground/60 text-center mb-12 max-w-3xl mx-auto">
              Common questions about our simple NDIS automation solutions
            </p>

            <Accordion type="single" collapsible className="w-full space-y-4">
              {ndisFaqs.map((faq, index) => (
                <AccordionItem
                  key={index}
                  value={`ndis-item-${index}`}
                  className="border border-border/30 rounded-lg px-6 py-2 bg-background/50"
                >
                  <AccordionTrigger className="text-lg font-medium text-left">
                    {faq.question}
                  </AccordionTrigger>
                  <AccordionContent className="text-foreground/70 pt-2 pb-4">
                    {faq.answer}
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-3xl font-bold mb-6">Ready to Simplify Your NDIS Practice?</h2>
            <p className="text-xl text-foreground/70 mb-8">
              Book a consultation to discuss how our simple automation solutions can help reduce your paperwork and administrative burden.
            </p>
            <Link
              href="/consultation"
              className="inline-flex items-center gap-2 px-8 py-4 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors text-lg font-medium"
            >
              Free Consultation
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </Container>
      </section>
    </main>
  );
}