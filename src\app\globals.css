@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Royal Sapphire & Gold Palette */
    --bg-primary: #080A12;
    --bg-alt-grad-0: #080A12;
    --bg-alt-grad-100: #101425;
    --surface-card: #141826;
    --accent-primary: #1A6BFF;
    --accent-secondary: #FFCB47;
    --text-heading: #FFFFFF;
    --text-body: #E8EDF7;
    --success: #26E0B8;
    --error: #FF697A;
    --border-hair: rgba(255,255,255,0.06);

    /* Legacy HSL values for shadcn/ui compatibility */
    --background: 225 50% 6%;
    --foreground: 225 25% 90%;
    --card: 225 35% 12%;
    --card-foreground: 225 25% 90%;
    --popover: 225 35% 12%;
    --popover-foreground: 225 25% 90%;
    --primary: 218 100% 55%;
    --primary-foreground: 0 0% 100%;
    --secondary: 45 100% 64%;
    --secondary-foreground: 225 50% 6%;
    --muted: 225 35% 12%;
    --muted-foreground: 225 25% 90%;
    --accent: 218 100% 55%;
    --accent-foreground: 0 0% 100%;
    --destructive: 351 100% 71%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 100% / 0.06;
    --input: 225 35% 12%;
    --ring: 218 100% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    background-color: var(--bg-primary);
    background-image:
      radial-gradient(circle at 20% 25%, rgba(26, 107, 255, 0.08) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(255, 203, 71, 0.06) 0%, transparent 50%);
  }

  h1, h2, h3, h4, h5, h6 {
    line-height: 1.3;
    letter-spacing: -0.02em;
  }

  h1 {
    line-height: 1.2;
  }

  p {
    line-height: 1.6;
  }
}

@layer utilities {
  /* Royal Sapphire & Gold Utility Classes - No Gradients */
  .text-accent-primary {
    color: var(--accent-primary);
  }

  .text-accent-secondary {
    color: var(--accent-secondary);
  }

  /* Golden accent text for premium touches */
  .text-gold-accent {
    color: var(--accent-secondary);
    font-weight: 600;
  }

  .text-gold-highlight {
    color: var(--accent-secondary);
    text-shadow: 0 0 10px rgba(255, 203, 71, 0.3);
  }

  .btn-royal-primary {
    @apply bg-accent-primary text-text-heading rounded-full px-6 shadow-md;
    @apply hover:bg-accent-secondary hover:text-bg-primary focus:shadow-lg;
    @apply transition-all duration-300;
  }

  .btn-royal-secondary {
    @apply bg-accent-secondary text-bg-primary rounded-full px-6 shadow-md;
    @apply hover:bg-accent-primary hover:text-text-heading focus:shadow-lg;
    @apply transition-all duration-300;
  }

  .card-royal {
    @apply bg-surface-card border border-border-hair rounded-2xl;
  }

  /* Updated glow effects with new colors */
  .shadow-glow-sm {
    box-shadow: 0 0 15px rgba(26, 107, 255, 0.2);
  }

  .shadow-glow-md {
    box-shadow: 0 0 25px rgba(26, 107, 255, 0.3);
  }

  .shadow-glow-lg {
    box-shadow: 0 0 35px rgba(26, 107, 255, 0.4);
  }

  .shadow-glow-gold {
    box-shadow: 0 0 20px rgba(255, 203, 71, 0.3);
  }

  .bg-grid {
    background-size: 50px 50px;
    background-image:
      linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }

  .animate-gradient-x {
    background-size: 400% 400%;
    animation: gradient-x 3s ease infinite;
  }

  .animate-shimmer {
    animation: shimmer 2s ease-in-out infinite;
  }

  @keyframes gradient-x {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%) skewX(-12deg);
    }
    100% {
      transform: translateX(200%) skewX(-12deg);
    }
  }

  .perspective {
    perspective: 1000px;
  }

  .rotate-3d {
    transform: rotateX(15deg) rotateY(15deg);
  }
}