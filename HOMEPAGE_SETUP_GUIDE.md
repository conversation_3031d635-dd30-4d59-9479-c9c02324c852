# Homepage Setup & Modification Guide
## Advisync AI Solutions - Mobile-First Design Documentation

### BRAND CONTEXT & REQUIREMENTS

**Company**: Advisync AI Solutions (Melbourne, Australia)
**Target Audience**: Australian small businesses (tradies, clinics, cafés, NDIS providers) without full-time receptionists or tech staff
**Voice & Tone**: Friendly expert, no jargon, Australian spelling
**Design Theme**: Dark theme (#0B0E19 background, #00E5FF accent)

### SERVICE PILLARS (ONLY TWO)
1. **AI Voice Agents** – 24/7 call handling, lead capture, bookings (Vapi/Retell AI)
2. **Workflow Automations** – n8n/Make.com flows removing manual data entry

### PRIMARY GOALS
1. Communicate "we automate your calls & admin so you earn more, stress less"
2. Drive discovery-call bookings (>5% conversion target)
3. Show real ROI via interactive savings calculator
4. Provide social proof through Australian small-business case studies

### MOBILE-FIRST DESIGN PRINCIPLES
- Start with mobile layout (320px+)
- Progressive enhancement for tablet (768px+) and desktop (1024px+)
- Touch-friendly buttons (minimum 44px height)
- Readable typography (minimum 16px base font size)
- Optimised images with responsive breakpoints
- Fast loading times (<3s on 4G)

### PAGE STRUCTURE (13 SECTIONS)

#### 1. STICKY NAVBAR
**Mobile**: Hamburger menu with slide-out drawer
**Desktop**: Horizontal navigation
**Elements**: Logo • Home • Voice Agents • Automations • Case Studies • Pricing • "Book a Call" CTA

#### 2. HERO SECTION
**H1**: "Your 24/7 AI Voice & Automation Partner"
**Sub-heading**: ~18 words highlighting local support and instant savings
**CTAs**: 
- Primary: "Get Free Strategy Call"
- Secondary: "Hear a 30-sec Demo" (modal with Twilio snippet)

#### 3. SAVINGS CALCULATOR
**Interactive Elements**:
- Monthly call volume input
- Average call duration (minutes)
- Hourly wage input
- Advisync plan fee selector
**Outputs**: Monthly + yearly savings display
**Optional**: Email capture for "Send my results"

#### 4. PAIN/BENEFIT STRIP
**Three Tiles**:
- No missed calls
- Zero paperwork
- Happier customers

#### 5. SERVICE PILLARS (2 CARDS)
**AI Voice Agents**:
- Lead capture
- Appointment booking
- FAQ answering

**Workflow Automations**:
- CRM updates
- Invoice sending
- SMS/email follow-ups

#### 6. WHY ADVISYNC? METRICS GRID
**Four Key Metrics**:
- 98% customer satisfaction
- 15-min average response
- 70% call-handling cost saved
- 14-day deployment

#### 7. CLIENT LOGO BAR
**Display**: 6 Australian small-business logos
**Mobile**: Horizontal scroll
**Desktop**: Grid layout

#### 8. CASE STUDY SLIDER
**Three Cards Each**:
- Client logo
- 40-word success story
- "Read More" link

#### 9. HOW IT WORKS (4-STEP TIMELINE)
**Process Steps**:
1. Discover
2. Build
3. Launch
4. Optimise

#### 10. PRICING PREVIEW
**Three Tiers**: Starter • Growth • Scale
**Include**: Minutes, workflows, priority support
**Note**: Prices in AUD, include GST note
**Exclude**: Twilio fees mention

#### 11. FAQ ACCORDION
**Six Questions**:
- Pricing details
- Setup timeframe
- Number porting
- Data security
- Integration capabilities
- Support availability

#### 12. FINAL CTA BANNER
**Message**: "Ready to automate? Book your free call."
**Design**: Full-width, contrasting background

#### 13. SEO FOOTER
**Elements**: Contact info, ABN, social links, mini-sitemap
**Schema**: Organisation + FAQ structured data

### DESIGN & COPY RULES

#### Typography
- Paragraphs ≤ 3 lines on desktop
- Minimum 16px base font size
- Clear hierarchy (H1 > H2 > H3)
- Line height 1.5+ for readability

#### Content Guidelines
- Active verbs only
- Zero buzzwords ("synergy", "leverage", etc.)
- Australian spelling throughout
- Conversational, friendly tone
- Technical accuracy without jargon

#### Visual Design
- Dark theme: #0B0E19 background
- Accent colour: #00E5FF
- High contrast for accessibility
- Consistent spacing system
- Mobile-optimised images

#### SEO Requirements
- Schema markup for FAQ + Organisation
- Meta descriptions <160 characters
- Semantic HTML structure
- Fast loading optimisation
- Local SEO elements (Melbourne, Australia)

### CONVERSION OPTIMISATION

#### CTA Strategy
- Primary CTA: "Get Free Strategy Call"
- Secondary CTAs throughout page
- Consistent button styling
- Clear value propositions

#### Trust Signals
- Australian business credentials
- Client testimonials
- Security certifications
- Local contact information

#### Performance Metrics
- Target >5% conversion rate
- Page load speed <3 seconds
- Mobile-friendly score 100%
- Accessibility compliance (WCAG AA)

### TECHNICAL IMPLEMENTATION NOTES

#### Framework Requirements
- Next.js with App Router
- TypeScript strict mode
- Tailwind CSS for styling
- Framer Motion for animations

#### Component Structure
- Reusable UI components
- Mobile-first responsive design
- Server-side rendering optimisation
- Progressive enhancement

#### Integration Points
- Vapi voice agent integration
- N8N workflow connections
- Analytics tracking (GA4)
- Form submission handling

### CONTENT DELIVERY FORMAT

All content should be delivered as a **single JSON array** with objects containing:
- `"id"`: kebab-case identifier
- `"component"`: component type (e.g., "hero", "calculator")
- `"copy"`: HTML-ready content string
- `"props"`: images, inputs, CTA labels, configuration
- `"seo"`: title & meta-description when applicable

### QUALITY CHECKLIST

Before implementation, verify:
- [ ] Mobile-first design principles applied
- [ ] All 13 sections included in correct order
- [ ] Australian spelling and terminology used
- [ ] Conversion goals clearly addressed
- [ ] SEO requirements met
- [ ] Accessibility standards followed
- [ ] Performance optimisation implemented
- [ ] Brand voice and tone consistent

### MODIFICATION WORKFLOW

When updating the homepage:
1. Review brand context and goals
2. Identify specific section(s) to modify
3. Apply mobile-first design principles
4. Test on multiple device sizes
5. Validate conversion impact
6. Update documentation as needed

This guide ensures consistent, conversion-focused homepage development that serves Australian small businesses effectively while maintaining technical excellence and user experience standards.
