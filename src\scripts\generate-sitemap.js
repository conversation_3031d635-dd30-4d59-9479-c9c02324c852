const { writeFileSync } = require('fs');
const { globby } = require('globby');
const prettier = require('prettier');

const DOMAIN = 'https://advisync.com.au';

async function generateSitemap() {
  const prettierConfig = await prettier.resolveConfig('./.prettierrc');
  
  // Pages to exclude from the sitemap
  const excludedPages = [
    '/api/',
    '/dashboard/',
    '/admin/',
  ];

  // Get all routes from pages directory
  const pages = await globby([
    'src/app/**/page.tsx',
    'src/app/**/route.ts',
    '!src/app/api/**/*', // Exclude API routes
  ]);

  const routes = pages
    .map((page) => {
      // Remove src/app and file extensions
      const path = page
        .replace('src/app', '')
        .replace('/page.tsx', '')
        .replace('/route.ts', '');
      
      // Skip excluded pages
      if (excludedPages.some(excluded => path.startsWith(excluded))) {
        return null;
      }

      // Convert /index to /
      const route = path === '/index' ? '' : path;

      // Define priority and changefreq based on route
      let priority = '0.7';
      let changefreq = 'weekly';

      // Homepage gets highest priority
      if (route === '') {
        priority = '1.0';
        changefreq = 'daily';
      }
      // Key service pages get high priority
      else if (route.startsWith('/services')) {
        priority = '0.9';
        changefreq = 'weekly';
        // NDIS and small business specific pages get highest service priority
        if (route.includes('ndis') || route.includes('small-business')) {
          priority = '0.95';
        }
      }
      // Blog and case studies get high priority for fresh content
      else if (route.startsWith('/blog') || route.startsWith('/case-studies')) {
        priority = '0.8';
        changefreq = 'daily';
      }
      // About and contact pages get medium-high priority
      else if (route.startsWith('/about') || route.startsWith('/contact')) {
        priority = '0.8';
        changefreq = 'weekly';
      }

      return `
        <url>
          <loc>${DOMAIN}${route}</loc>
          <lastmod>${new Date().toISOString()}</lastmod>
          <changefreq>${changefreq}</changefreq>
          <priority>${priority}</priority>
        </url>`;
    })
    .filter(Boolean)
    .join('');

  const sitemap = `
    <?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${routes}
    </urlset>
  `;

  const formatted = await prettier.format(sitemap, {
    ...prettierConfig,
    parser: 'html',
  });

  writeFileSync('public/sitemap.xml', formatted);
  console.log('✅ Sitemap generated successfully!');
}

generateSitemap(); 