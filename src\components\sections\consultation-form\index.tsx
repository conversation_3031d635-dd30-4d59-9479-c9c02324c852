'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Calendar,
  Clock,
  MessageSquare,
  Send,
  Loader2,
  CheckCircle2,
  Building2,
  Users,
  User,
  Mail,
  Phone,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  FileText,
  BarChart,
  ChevronDown,
  ChevronUp,
  Globe,
  CreditCard,
  Zap,
  Bot,
  Heart,
  LucideIcon
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { cn } from '@/lib/utils';

interface FormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  teamSize: string;
  service: string;
  message: string;
  date: string;
  time: string;
}

const initialFormData: FormData = {
  name: '',
  email: '',
  phone: '',
  company: '',
  teamSize: '',
  service: '',
  message: '',
  date: '',
  time: '',
};

interface ServiceOption {
  value: string;
  label: string;
  description: string;
  icon: LucideIcon;
  complexity: string;
  timeframe: string;
}

interface ServiceCategory {
  category: string;
  options: ServiceOption[];
}

const services: ServiceCategory[] = [
  {
    category: "AI Voice Solutions",
    options: [
      {
        value: "ai-voice-agents",
        label: "AI Voice Agents",
        description: "24/7 AI receptionists that never miss a call, book appointments, and handle customer inquiries. Perfect for tradies and healthcare providers.",
        icon: Bot,
        complexity: "Professional",
        timeframe: "1-2 weeks"
      },
      {
        value: "voice-starter",
        label: "Voice Agent Starter",
        description: "Basic AI voice agent setup with call handling and appointment booking. Ideal for small businesses getting started.",
        icon: Phone,
        complexity: "Simple",
        timeframe: "1 week"
      }
    ]
  },
  {
    category: "Business Automation",
    options: [
      {
        value: "workflow-automation",
        label: "AI Workflow Automation",
        description: "Simple automation that connects your business tools and eliminates repetitive paperwork. Save 5-10 hours per week.",
        icon: Zap,
        complexity: "Simple",
        timeframe: "1-2 weeks"
      },
      {
        value: "customer-management",
        label: "Customer Management Automation",
        description: "Capture every lead, automate follow-ups, and turn more inquiries into paying customers. Works perfectly with AI voice agents.",
        icon: Heart,
        complexity: "Professional",
        timeframe: "2-3 weeks"
      },
      {
        value: "appointment-automation",
        label: "Appointment & Booking Automation",
        description: "Automated scheduling, confirmations, and reminders to reduce no-shows by 40%. Perfect for healthcare and service businesses.",
        icon: Calendar,
        complexity: "Simple",
        timeframe: "1 week"
      }
    ]
  },
  {
    category: "Free Consultation",
    options: [
      {
        value: "free-consultation",
        label: "Free Business Consultation",
        description: "30-minute consultation to identify how AI voice agents and automation can help your business capture more leads and save time.",
        icon: CheckCircle2,
        complexity: "Free",
        timeframe: "30 minutes"
      }
    ]
  }
];

const teamSizes = [
  { value: '1', label: 'Just me' },
  { value: '2-5', label: '2-5 employees' },
  { value: '6-10', label: '6-10 employees' },
  { value: '11-25', label: '11-25 employees' },
  { value: '26+', label: '26+ employees' }
];

const contactPreferences = [
  'Morning (9AM - 12PM)',
  'Afternoon (12PM - 5PM)',
  'Evening (After 5PM)',
  'Any time',
  'Weekdays only',
  'Weekends preferred'
];

const benefits = [
  {
    icon: Clock,
    title: 'Quick Response',
    description: 'Get a response within 24 hours'
  },
  {
    icon: Calendar,
    title: 'Flexible Scheduling',
    description: 'Choose a time that suits you'
  },
  {
    icon: MessageSquare,
    title: 'Expert Consultation',
    description: 'Speak with our automation specialists'
  },
  {
    icon: CheckCircle2,
    title: 'No Obligation',
    description: 'Free consultation with no commitments'
  }
];

// Add icons for contact preference times
const contactPreferencesWithIcons = [
  { value: 'Morning (9AM - 12PM)', icon: <span className="text-yellow-500">☀️</span> },
  { value: 'Afternoon (12PM - 5PM)', icon: <span className="text-orange-500">🌤️</span> },
  { value: 'Evening (After 5PM)', icon: <span className="text-indigo-500">🌙</span> },
  { value: 'Any time', icon: <span>⏱️</span> },
  { value: 'Weekdays only', icon: <span>📅</span> },
  { value: 'Weekends preferred', icon: <span>🏖️</span> }
];

export function ConsultationForm() {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 3;

  const validateStep = (step: number) => {
    const newErrors: Partial<FormData> = {};

    if (step === 1) {
      if (!formData.name) newErrors.name = 'Name is required';
      if (!formData.email) {
        newErrors.email = 'Email is required';
      } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
        newErrors.email = 'Email is invalid';
      }
      if (!formData.phone) newErrors.phone = 'Phone number is required';
    } else if (step === 2) {
      if (!formData.company) newErrors.company = 'Company name is required';
      if (!formData.teamSize) newErrors.teamSize = 'Team size is required';
      if (!formData.service) newErrors.service = 'Please select a service';
    } else if (step === 3) {
      if (!formData.time) newErrors.time = 'Please select a preferred time';
      if (!formData.message) newErrors.message = 'Please provide some information about your needs';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateStep(currentStep)) return;

    // For the final step, submit the form
    if (currentStep === totalSteps) {
      setIsSubmitting(true);
      
      try {
        // Find the selected service details for the confirmation message
        let selectedService: ServiceOption | undefined;
        let selectedCategory: string = "";
        
        for (const category of services) {
          const found = category.options.find(opt => opt.value === formData.service);
          if (found) {
            selectedService = found;
            selectedCategory = category.category;
            break;
          }
        }
        
        // Send the form data to the API
        const response = await fetch('/api/save-consultation', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ...formData,
            serviceName: selectedService?.label,
            serviceCategory: selectedCategory,
            submitDate: new Date().toISOString()
          })
        });
        
        const data = await response.json();
        
        if (response.ok && data.success) {
          // Scroll to top for better user experience with the notification
          window.scrollTo({ top: 0, behavior: 'smooth' });
          
          // Show success message to the user
          setSubmitStatus('success');
          
          // Reset form after successful submission (but don't hide success message)
          // This happens when user clicks "Dismiss" button now
          setFormData(initialFormData);
          setCurrentStep(1);
        } else {
          // Show error if the API returned an error
          console.error('API returned error:', data);
          setSubmitStatus('error');
          
          // Reset error state after delay
          setTimeout(() => {
            setSubmitStatus('idle');
          }, 5000);
        }
      } catch (error) {
        console.error('Error submitting form:', error);
        
        // Show error to the user
        setSubmitStatus('error');
        
        // Reset error state after delay
        setTimeout(() => {
          setSubmitStatus('idle');
        }, 5000);
      } finally {
        setIsSubmitting(false);
      }
    } else {
      // Move to the next step
      setCurrentStep(prev => prev + 1);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const renderStepIndicator = () => {
    return (
      <div className="flex items-center justify-center mb-8">
        {Array.from({ length: totalSteps }).map((_, index) => (
          <div key={index} className="flex items-center">
            <div
              className={cn(
                "w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold transition-all duration-300 border-2",
                currentStep > index + 1
                  ? "bg-accent-secondary text-bg-primary border-accent-secondary shadow-lg shadow-accent-secondary/25"
                  : currentStep === index + 1
                    ? "bg-accent-primary text-white border-accent-primary shadow-lg shadow-accent-primary/25"
                    : "bg-background text-foreground/60 border-border"
              )}
            >
              {currentStep > index + 1 ? <CheckCircle className="w-5 h-5" /> : index + 1}
            </div>
            {index < totalSteps - 1 && (
              <div
                className={cn(
                  "h-2 w-12 mx-2 rounded-full transition-all duration-300",
                  currentStep > index + 1 ? "bg-accent-secondary" : "bg-border"
                )}
              />
            )}
          </div>
        ))}
      </div>
    );
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6 md:space-y-8"
          >
            <h3 className="text-lg md:text-xl font-medium text-center">Your Contact Information</h3>
            
            {/* Name Input */}
            <div>
              <label htmlFor="name" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <User className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Your Name</span>
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200",
                  "hover:border-accent-primary/60",
                  errors.name && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                placeholder="John Doe"
              />
              {errors.name && (
                <p className="mt-2 text-sm text-destructive">{errors.name}</p>
              )}
            </div>

            {/* Email Input */}
            <div>
              <label htmlFor="email" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Mail className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Email Address</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200",
                  "hover:border-accent-primary/60",
                  errors.email && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="mt-2 text-sm text-destructive">{errors.email}</p>
              )}
            </div>

            {/* Phone Input */}
            <div>
              <label htmlFor="phone" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Phone className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Phone Number</span>
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200",
                  "hover:border-accent-primary/60",
                  errors.phone && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                placeholder="+61 XXX XXX XXX"
              />
              {errors.phone && (
                <p className="mt-2 text-sm text-destructive">{errors.phone}</p>
              )}
            </div>
          </motion.div>
        );
      case 2:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6 md:space-y-8"
          >
            <h3 className="text-lg md:text-xl font-medium text-center">Your Business Details</h3>
            
            {/* Company Input */}
            <div>
              <label htmlFor="company" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Building2 className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Company Name</span>
              </label>
              <input
                type="text"
                id="company"
                name="company"
                value={formData.company}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200",
                  "hover:border-accent-primary/60",
                  errors.company && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                placeholder="Your Company"
              />
              {errors.company && (
                <p className="mt-2 text-sm text-destructive">{errors.company}</p>
              )}
            </div>

            {/* Team Size Select */}
            <div>
              <label htmlFor="teamSize" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Users className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Team Size</span>
              </label>
              <select
                id="teamSize"
                name="teamSize"
                value={formData.teamSize}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200 appearance-none",
                  "hover:border-accent-primary/60",
                  "bg-no-repeat bg-[position:right_0.75rem_center] bg-[size:1rem]",
                  "bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%231A6BFF%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%231A6BFF%22%3E%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22M19.5%208.25l-7.5%207.5-7.5-7.5%22%20%2F%3E%3C%2Fsvg%3E')]",
                  errors.teamSize && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
              >
                <option value="" disabled>Select team size</option>
                {teamSizes.map(size => (
                  <option key={size.value} value={size.value}>{size.label}</option>
                ))}
              </select>
              {errors.teamSize && (
                <p className="mt-2 text-sm text-destructive">{errors.teamSize}</p>
              )}
            </div>

            {/* Service Select Dropdown */}
            <div>
              <label htmlFor="service" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Zap className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">What service are you interested in?</span>
              </label>
              <select
                id="service"
                name="service"
                value={formData.service}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200 appearance-none",
                  "hover:border-accent-primary/60",
                  "bg-no-repeat bg-[position:right_0.75rem_center] bg-[size:1rem]",
                  "bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%231A6BFF%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%231A6BFF%22%3E%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22M19.5%208.25l-7.5%207.5-7.5-7.5%22%20%2F%3E%3C%2Fsvg%3E')]",
                  errors.service && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                required
              >
                <option value="" disabled>Please select a service *</option>
                {services.map((category, index) => (
                  <optgroup key={index} label={category.category} className="font-medium text-primary">
                    {category.options.map((service, serviceIndex) => (
                      <option key={`${index}-${serviceIndex}`} value={service.value} className="text-foreground font-normal">
                        {service.label} ({service.complexity} • {service.timeframe})
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
              {errors.service && (
                <p className="mt-2 text-sm text-destructive">{errors.service}</p>
              )}
            </div>

            {/* Service Details */}
            {formData.service && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                className="border-2 border-accent-primary/30 rounded-lg overflow-hidden bg-accent-primary/5 shadow-lg shadow-accent-primary/10"
              >
                {(() => {
                  // Find selected service details
                  let selectedService: ServiceOption | undefined;
                  let selectedCategory: string = "";
                  
                  for (const category of services) {
                    const found = category.options.find(opt => opt.value === formData.service);
                    if (found) {
                      selectedService = found;
                      selectedCategory = category.category;
                      break;
                    }
                  }
                  
                  if (!selectedService) return null;
                  
                  return (
                    <div>
                      <div className="bg-accent-primary/15 px-4 py-3 border-b border-accent-primary/20">
                        <span className="text-sm font-semibold text-accent-primary">{selectedCategory}</span>
                      </div>
                      <div className="p-4 space-y-4">
                        <div className="flex items-center">
                          <selectedService.icon className="w-6 h-6 mr-3 text-accent-primary" />
                          <span className="font-semibold text-lg text-accent-primary">{selectedService.label}</span>
                        </div>
                        <p className="text-sm text-foreground/80 leading-relaxed">{selectedService.description}</p>
                        <div className="flex flex-wrap items-center gap-3 mt-4 pt-3 border-t border-accent-primary/20">
                          <div>
                            <span className={cn(
                              "inline-flex items-center text-xs px-3 py-1.5 rounded-full font-medium",
                              selectedService.complexity === "Simple" ? "bg-green-500/10 text-green-600 border border-green-500/20" :
                              selectedService.complexity === "Professional" ? "bg-accent-primary/10 text-accent-primary border border-accent-primary/20" :
                              selectedService.complexity === "Free" ? "bg-accent-secondary/10 text-accent-secondary border border-accent-secondary/20" :
                              "bg-orange-500/10 text-orange-600 border border-orange-500/20"
                            )}>
                              <Zap className="h-3 w-3 mr-1" />
                              {selectedService.complexity}
                            </span>
                          </div>
                          <div className="flex items-center text-accent-primary">
                            <Clock className="h-4 w-4 mr-2" />
                            <span className="text-sm font-medium">
                              {selectedService.timeframe}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </motion.div>
        );
      case 3:
        return (
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
            className="space-y-6 md:space-y-8"
          >
            <h3 className="text-lg md:text-xl font-medium text-center">Schedule Your Session</h3>
            
            {/* Time Preference Selection with Dropdown */}
            <div>
              <label htmlFor="time" className="block text-sm md:text-base font-semibold mb-2 md:mb-3 flex items-center">
                <Clock className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Preferred Contact Time</span>
              </label>
              <select
                id="time"
                name="time"
                value={formData.time}
                onChange={handleChange}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200 appearance-none",
                  "hover:border-accent-primary/60",
                  "bg-no-repeat bg-[position:right_0.75rem_center] bg-[size:1rem]",
                  "bg-[url('data:image/svg+xml;charset=utf-8,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20fill%3D%22%231A6BFF%22%20viewBox%3D%220%200%2024%2024%22%20stroke-width%3D%221.5%22%20stroke%3D%22%231A6BFF%22%3E%3Cpath%20stroke-linecap%3D%22round%22%20stroke-linejoin%3D%22round%22%20d%3D%22M19.5%208.25l-7.5%207.5-7.5-7.5%22%20%2F%3E%3C%2Fsvg%3E')]",
                  errors.time && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
              >
                <option value="" disabled>Select preferred time</option>
                {contactPreferencesWithIcons.map((preference, index) => (
                  <option key={index} value={preference.value}>
                    {preference.value}
                  </option>
                ))}
              </select>
              {errors.time && (
                <p className="mt-2 text-sm text-destructive">{errors.time}</p>
              )}
            </div>

            {/* Additional Message */}
            <div>
              <label htmlFor="message" className="block text-sm md:text-base font-semibold mb-2 flex items-center">
                <MessageSquare className="w-4 h-4 mr-2 text-accent-secondary" />
                <span className="text-accent-secondary">Additional Information</span>
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                rows={4}
                className={cn(
                  "w-full px-4 py-3 md:py-4 text-base rounded-lg bg-background border-2 border-border focus:border-accent-primary focus:ring-2 focus:ring-accent-primary/20 outline-none transition-all duration-200 resize-none",
                  "hover:border-accent-primary/60",
                  errors.message && "border-destructive focus:border-destructive focus:ring-destructive/20"
                )}
                placeholder="Tell us about your project or specific needs..."
              />
              {errors.message && (
                <p className="mt-2 text-sm text-destructive">{errors.message}</p>
              )}
            </div>
          </motion.div>
        );
      default:
        return null;
    }
  };

  const renderButtons = () => {
    return (
      <div className="flex justify-between mt-8">
        {currentStep > 1 ? (
          <button
            type="button"
            onClick={handlePrevStep}
            className="px-4 py-2.5 text-sm md:text-base font-medium text-foreground/70 hover:text-foreground transition-colors"
          >
            Back
          </button>
        ) : (
          <div></div>
        )}
        
        {currentStep < totalSteps ? (
          <button
            type="button"
            onClick={handleNextStep}
            className="flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-accent-primary text-white rounded-full hover:bg-accent-primary/90 hover:shadow-lg hover:shadow-accent-primary/25 transition-all duration-200 text-sm md:text-base font-semibold"
          >
            <span>Next</span>
            <ArrowRight className="ml-2 w-4 h-4" />
          </button>
        ) : (
          <button
            type="submit"
            disabled={isSubmitting}
            className="flex items-center px-6 sm:px-8 py-3 sm:py-4 bg-accent-secondary text-bg-primary rounded-full hover:bg-accent-secondary/90 hover:shadow-lg hover:shadow-accent-secondary/25 transition-all duration-200 disabled:opacity-70 disabled:cursor-not-allowed text-sm md:text-base font-semibold"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                <span>Submitting...</span>
              </>
            ) : (
              <>
                <span className="hidden sm:inline">Schedule Strategy Session</span>
                <span className="sm:hidden">Schedule</span>
                <Send className="ml-2 h-4 w-4" />
              </>
            )}
          </button>
        )}
      </div>
    );
  };

  if (submitStatus === 'success') {
    // Find the selected service details
    let selectedService: ServiceOption | undefined;
    let selectedCategory: string = "";
    
    for (const category of services) {
      const found = category.options.find(opt => opt.value === formData.service);
      if (found) {
        selectedService = found;
        selectedCategory = category.category;
        break;
      }
    }
    
    return (
      <motion.div
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4, ease: "easeOut" }}
        className="fixed inset-x-0 bottom-0 z-50 p-4 mx-auto max-w-md"
      >
        <div className="bg-background border border-primary/20 rounded-xl shadow-lg shadow-primary/10 p-5 mb-4">
          <div className="flex flex-col items-center">
            <div className="bg-green-500/10 p-3 rounded-full mb-4">
              <CheckCircle2 className="h-10 w-10 text-green-500" />
            </div>
            
            <h3 className="text-lg font-bold mb-3 text-center">
              {formData.service === 'free-starter-assessment' 
                ? "Assessment Request Received!" 
                : "Strategy Session Scheduled!"}
            </h3>
            
            <p className="text-sm text-center mb-4">
              {formData.service === 'free-starter-assessment'
                ? "We'll evaluate your automation opportunities within 48 hours."
                : "Thank you for scheduling your complimentary strategy session."}
            </p>
            
            <div className="w-full bg-primary/5 rounded-lg p-3 mb-3">
              <p className="text-sm flex justify-between">
                <span className="text-foreground/70">Preferred time:</span>
                <span className="font-medium">{formData.time}</span>
              </p>
            </div>
            
            {selectedService && (
              <div className="w-full bg-primary/5 rounded-lg p-3 mb-3">
                <p className="text-sm flex justify-between">
                  <span className="text-foreground/70">Service:</span>
                  <span className="font-medium">{selectedService.label}</span>
                </p>
              </div>
            )}
            
            <div className="w-full bg-primary/5 rounded-lg p-3">
              <p className="text-sm flex justify-between">
                <span className="text-foreground/70">Confirmation sent to:</span>
                <span className="font-medium">{formData.email}</span>
              </p>
            </div>
            
            <button 
              className="mt-4 text-xs text-primary hover:text-primary/80 transition-colors"
              onClick={() => setSubmitStatus('idle')}
            >
              Dismiss
            </button>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <div id="consultation-form" className="bg-secondary/5 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-primary/10 w-full mx-auto">
      {submitStatus === 'error' && (
        <div className="mb-6 p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-500">Submission Failed</h3>
              <p className="text-foreground/70 mt-1">
                There was an error submitting your form. Please check your inputs and try again.
              </p>
            </div>
          </div>
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="w-full mx-auto">
        {renderStepIndicator()}
        <div className="mt-6 md:mt-8">
          {renderStepContent()}
        </div>
        {renderButtons()}
      </form>
    </div>
  );
} 