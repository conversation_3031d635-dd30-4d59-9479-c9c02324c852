'use client';

import React from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';
import { X } from 'lucide-react';

interface Process {
  title: string;
  description: string;
}

export interface Metric {
  label: string;
  value: string;
  unit: string;
}

export interface CaseStudyResults {
  summary: string;
  details: string[];
  testimonial: {
    quote: string;
    author: string;
    role: string;
    company: string;
  };
  gallery: string[];
}

export interface CaseStudy {
  title: string;
  description: string;
  image: string;
  category: string;
  client: string;
  metrics: Metric[];
  challenge: string;
  solution: string;
  process: Process[];
  results: string;
  technologies: string[];
  images: {
    main: string;
    gallery?: string[];
  };
  tags: string[];
}

interface CaseStudyModalProps {
  isOpen: boolean;
  onClose: () => void;
  caseStudy: CaseStudy;
}

export function CaseStudyModal({ isOpen, onClose, caseStudy }: CaseStudyModalProps) {
  const modalRef = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="fixed inset-0 z-50 flex items-center justify-center p-4 sm:p-6"
          >
            <div
              ref={modalRef}
              className="relative bg-background border border-border/50 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto"
            >
              {/* Close Button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 p-2 text-foreground/60 hover:text-foreground transition-colors z-10 bg-background/50 rounded-full"
              >
                <X size={20} />
              </button>

              {/* Content */}
              <div className="p-6 sm:p-8">
                {/* Header */}
                <div className="relative w-full aspect-video rounded-lg overflow-hidden mb-8">
                  <Image
                    src={caseStudy.images.main}
                    alt={caseStudy.title}
                    fill
                    className="object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent" />
                  <div className="absolute bottom-0 left-0 p-6">
                    <div className="text-sm font-medium text-primary mb-2">
                      {caseStudy.category}
                    </div>
                    <h2 className="text-2xl sm:text-3xl font-bold mb-2">
                      {caseStudy.title}
                    </h2>
                    <p className="text-foreground/60">
                      {caseStudy.client}
                    </p>
                  </div>
                </div>

                {/* Project Overview */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4">Project Overview</h3>
                  <p className="text-foreground/60">
                    {caseStudy.description}
                  </p>
                </div>

                {/* Metrics Grid */}
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mb-8">
                  {caseStudy.metrics.map((metric, index) => (
                    <motion.div
                      key={metric.label}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="bg-primary/5 rounded-lg p-4 text-center"
                    >
                      <div className="text-2xl font-bold text-accent-secondary">
                        {metric.value}
                      </div>
                      <div className="text-sm text-foreground/60">
                        {metric.label}
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Challenge & Solution */}
                <div className="grid sm:grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="text-xl font-semibold mb-4">The Challenge</h3>
                    <p className="text-foreground/60">
                      {caseStudy.challenge}
                    </p>
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-4">Our Solution</h3>
                    <p className="text-foreground/60">
                      {caseStudy.solution}
                    </p>
                  </div>
                </div>

                {/* Implementation Process */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-6">Implementation Process</h3>
                  <div className="space-y-6">
                    {caseStudy.process.map((step, index) => (
                      <motion.div
                        key={step.title}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="flex items-start space-x-4"
                      >
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-primary/10 text-primary flex items-center justify-center">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-semibold mb-1">{step.title}</h4>
                          <p className="text-foreground/60">{step.description}</p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </div>

                {/* Image Gallery */}
                {caseStudy.images.gallery && (
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold mb-4">Project Gallery</h3>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
                      {caseStudy.images.gallery.map((image, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ delay: index * 0.1 }}
                          className="relative aspect-video rounded-lg overflow-hidden"
                        >
                          <Image
                            src={image}
                            alt={`${caseStudy.title} gallery image ${index + 1}`}
                            fill
                            className="object-cover"
                          />
                        </motion.div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Technologies */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4">Technologies Used</h3>
                  <div className="flex flex-wrap gap-2">
                    {caseStudy.technologies.map((tech) => (
                      <span
                        key={tech}
                        className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Results */}
                <div className="mb-8">
                  <h3 className="text-xl font-semibold mb-4">Results</h3>
                  <p className="text-foreground/60">
                    {caseStudy.results}
                  </p>
                </div>
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
} 