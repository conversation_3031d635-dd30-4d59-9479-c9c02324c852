'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare,
  Send,
  Loader2,
  CheckCircle2,
  User,
  Mail,
  Phone,
  Building2
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface FormData {
  name: string;
  email: string;
  phone: string;
  company: string;
  service: string;
  message: string;
}

const initialFormData: FormData = {
  name: '',
  email: '',
  phone: '',
  company: '',
  service: '',
  message: ''
};

const services = [
  { value: 'general-inquiry', label: 'General Inquiry' },
  { value: 'business-automation', label: 'Business Automation' },
  { value: 'web-development', label: 'Web Development' },
  { value: 'ai-solutions', label: 'AI Solutions' },
  { value: 'support', label: 'Technical Support' }
];

export function ContactForm() {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const validateForm = () => {
    const newErrors: Partial<FormData> = {};

    if (!formData.name.trim() || formData.name.length < 2) {
      newErrors.name = 'Name is required (minimum 2 characters)';
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }

    if (!formData.company.trim() || formData.company.length < 2) {
      newErrors.company = 'Company name is required (minimum 2 characters)';
    }

    if (!formData.phone.trim() || formData.phone.length < 10) {
      newErrors.phone = 'Phone number is required (minimum 10 digits)';
    }

    if (!formData.service) {
      newErrors.service = 'Please select a service';
    }

    if (!formData.message.trim() || formData.message.length < 10) {
      newErrors.message = 'Message is required (minimum 10 characters)';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitStatus('idle');

    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (response.ok) {
        setSubmitStatus('success');
        setFormData(initialFormData);
      } else {
        console.error('Form submission error:', data);
        setSubmitStatus('error');
      }
    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitStatus('error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    if (errors[name as keyof FormData]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  if (submitStatus === 'success') {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="text-center py-8"
      >
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle2 className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-2xl font-bold mb-2">Message Sent!</h3>
        <p className="text-foreground/70 mb-6">
          Thank you for reaching out. We've received your message and will get back to you within 24 hours.
        </p>
        <button
          onClick={() => setSubmitStatus('idle')}
          className="px-6 py-2 bg-primary text-white rounded-full hover:bg-primary/90 transition-colors"
        >
          Send Another Message
        </button>
      </motion.div>
    );
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-6">
        {/* Name Input */}
        <div>
          <label htmlFor="name" className="block text-sm font-medium mb-2 flex items-center">
            <User className="w-4 h-4 mr-2 text-primary" />
            Your Name
          </label>
          <input
            type="text"
            id="name"
            name="name"
            value={formData.name}
            onChange={handleChange}
            className={cn(
              "w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",
              errors.name && "border-destructive focus:border-destructive focus:ring-destructive"
            )}
            placeholder="John Doe"
          />
          {errors.name && (
            <p className="mt-1 text-sm text-destructive">{errors.name}</p>
          )}
        </div>

        {/* Email Input */}
        <div>
          <label htmlFor="email" className="block text-sm font-medium mb-2 flex items-center">
            <Mail className="w-4 h-4 mr-2 text-primary" />
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleChange}
            className={cn(
              "w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",
              errors.email && "border-destructive focus:border-destructive focus:ring-destructive"
            )}
            placeholder="<EMAIL>"
          />
          {errors.email && (
            <p className="mt-1 text-sm text-destructive">{errors.email}</p>
          )}
        </div>

        {/* Phone Input (Optional) */}
        <div>
          <label htmlFor="phone" className="block text-sm font-medium mb-2 flex items-center">
            <Phone className="w-4 h-4 mr-2 text-primary" />
            Phone Number <span className="text-xs text-foreground/50 ml-1">(Optional)</span>
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleChange}
            className="w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors"
            placeholder="+61 XXX XXX XXX"
          />
        </div>

        {/* Company Input (Optional) */}
        <div>
          <label htmlFor="company" className="block text-sm font-medium mb-2 flex items-center">
            <Building2 className="w-4 h-4 mr-2 text-primary" />
            Company <span className="text-xs text-foreground/50 ml-1">(Optional)</span>
          </label>
          <input
            type="text"
            id="company"
            name="company"
            value={formData.company}
            onChange={handleChange}
            className="w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors"
            placeholder="Your Company"
          />
        </div>

        {/* Service Select */}
        <div>
          <label htmlFor="service" className="block text-sm font-medium mb-2">
            What can we help you with?
          </label>
          <select
            id="service"
            name="service"
            value={formData.service}
            onChange={handleChange}
            className={cn(
              "w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors",
              errors.service && "border-destructive focus:border-destructive focus:ring-destructive"
            )}
          >
            <option value="" disabled>Select a service</option>
            {services.map(service => (
              <option key={service.value} value={service.value}>{service.label}</option>
            ))}
          </select>
          {errors.service && (
            <p className="mt-1 text-sm text-destructive">{errors.service}</p>
          )}
        </div>

        {/* Message Textarea */}
        <div>
          <label htmlFor="message" className="block text-sm font-medium mb-2 flex items-center">
            <MessageSquare className="w-4 h-4 mr-2 text-primary" />
            Your Message
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleChange}
            rows={5}
            className={cn(
              "w-full px-4 py-3 rounded-lg bg-background border border-border focus:border-primary focus:ring-1 focus:ring-primary outline-none transition-colors resize-none",
              errors.message && "border-destructive focus:border-destructive focus:ring-destructive"
            )}
            placeholder="Please share your questions or details about your project."
          />
          {errors.message && (
            <p className="mt-1 text-sm text-destructive">{errors.message}</p>
          )}
        </div>
      </div>

      {/* Submit Button */}
      <button
        type="submit"
        disabled={isSubmitting}
        className="w-full flex items-center justify-center px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
      >
        {isSubmitting ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            <span>Sending...</span>
          </>
        ) : (
          <>
            <span>Send Message</span>
            <Send className="ml-2 h-4 w-4" />
          </>
        )}
      </button>
      
      {submitStatus === 'error' && (
        <div className="mt-4 p-3 bg-red-500/10 border border-red-500/30 rounded-lg text-center text-red-500">
          Something went wrong. Please try again.
        </div>
      )}
    </form>
  );
} 