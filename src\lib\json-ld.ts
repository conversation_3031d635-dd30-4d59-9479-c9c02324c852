import { siteConfig } from '@/config/metadata';

type Organization = {
  '@context': 'https://schema.org';
  '@type': 'Organization';
  name: string;
  url: string;
  logo: string;
  description: string;
  address: {
    '@type': 'PostalAddress';
    addressLocality: string;
    addressRegion: string;
    addressCountry: string;
  };
  contactPoint: {
    '@type': 'ContactPoint';
    telephone: string;
    contactType: string;
    email: string;
  };
  sameAs: string[];
};

type LocalBusiness = {
  '@context': 'https://schema.org';
  '@type': 'LocalBusiness';
  name: string;
  image: string;
  '@id': string;
  url: string;
  telephone: string;
  priceRange: string;
  address: {
    '@type': 'PostalAddress';
    streetAddress: string;
    addressLocality: string;
    addressRegion: string;
    postalCode: string;
    addressCountry: string;
  };
  geo: {
    '@type': 'GeoCoordinates';
    latitude: number;
    longitude: number;
  };
  openingHoursSpecification: {
    '@type': 'OpeningHoursSpecification';
    dayOfWeek: string[];
    opens: string;
    closes: string;
  };
  sameAs: string[];
  areaServed?: {
    '@type': string;
    name: string;
  };
  taxID?: string;
};

export const organizationLd: Organization = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Advisync AI Solutions',
  url: 'https://advisync.com.au',
  logo: 'https://advisync.com.au/logo.png',
  description: "Melbourne's trusted AI-automation agency specializing in voice agents, workflow automation, and digital solutions for Australian businesses.",
  address: {
    '@type': 'PostalAddress',
    addressLocality: 'Melbourne',
    addressRegion: 'VIC',
    addressCountry: 'AU'
  },
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+**************',
    contactType: 'customer service',
    email: '<EMAIL>'
  },
  sameAs: [
    'https://www.linkedin.com/company/advisync-solutions',
    'https://www.facebook.com/profile.php?id=61573127892569',
    'https://x.com/Advisync_AI_Sol',
    'https://www.instagram.com/advisync_solutions_/',
    'https://g.page/advisync'
  ]
};

export const localBusinessLd: LocalBusiness = {
  '@context': 'https://schema.org',
  '@type': 'LocalBusiness',
  name: 'Advisync AI Solutions',
  image: 'https://advisync.com.au/logo.png',
  '@id': 'https://advisync.com.au',
  url: 'https://advisync.com.au',
  telephone: '+**************',
  priceRange: '$$',
  address: {
    '@type': 'PostalAddress',
    streetAddress: '',
    addressLocality: 'Melbourne',
    addressRegion: 'VIC',
    postalCode: '3000',
    addressCountry: 'AU'
  },
  geo: {
    '@type': 'GeoCoordinates',
    latitude: -37.8136,
    longitude: 144.9631
  },
  openingHoursSpecification: {
    '@type': 'OpeningHoursSpecification',
    dayOfWeek: [
      'Monday',
      'Tuesday',
      'Wednesday',
      'Thursday',
      'Friday'
    ],
    opens: '09:00',
    closes: '17:00'
  },
  sameAs: [
    'https://www.linkedin.com/company/advisync-solutions',
    'https://www.facebook.com/profile.php?id=61573127892569',
    'https://x.com/Advisync_AI_Sol',
    'https://www.instagram.com/advisync_solutions_/',
    'https://g.page/advisync'
  ],
  areaServed: {
    '@type': 'State',
    name: 'Victoria'
  },
  taxID: '12 ***********'
};

export interface WebPage {
  '@context': 'https://schema.org';
  '@type': 'WebPage';
  name: string;
  description: string;
  url: string;
  publisher: Organization;
}

export function generateWebPageLd(
  name: string,
  description: string,
  url: string,
): WebPage {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebPage',
    name,
    description,
    url,
    publisher: organizationLd,
  };
}

export interface BlogPosting {
  '@context': 'https://schema.org';
  '@type': 'BlogPosting';
  headline: string;
  description: string;
  image: string;
  datePublished: string;
  dateModified: string;
  author: {
    '@type': 'Person';
    name: string;
    description?: string;
    image?: string;
  };
  publisher: Organization;
  mainEntityOfPage: {
    '@type': 'WebPage';
    '@id': string;
  };
  keywords: string;
}

export function generateBlogPostLd(
  post: {
    title: string;
    description: string;
    image: string;
    date: string;
    lastUpdated?: string;
    author: {
      name: string;
      bio?: string;
      image?: string;
    };
    tags: string[];
    id: string;
  },
): BlogPosting {
  return {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.description,
    image: post.image,
    datePublished: post.date,
    dateModified: post.lastUpdated || post.date,
    author: {
      '@type': 'Person',
      name: post.author.name,
      description: post.author.bio,
      image: post.author.image,
    },
    publisher: organizationLd,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `${siteConfig.url}/blog/${post.id}`,
    },
    keywords: post.tags.join(', '),
  };
}

export interface Service {
  '@context': 'https://schema.org';
  '@type': 'Service';
  name: string;
  description: string;
  provider: Organization;
  areaServed: {
    '@type': 'City';
    name: string;
  };
  image: string;
}

export function generateServiceLd(
  service: {
    title: string;
    description: string;
    image: string;
  },
): Service {
  return {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: service.title,
    description: service.description,
    provider: organizationLd,
    areaServed: {
      '@type': 'City',
      name: 'Melbourne',
    },
    image: service.image,
  };
}

export interface FAQPage {
  '@context': 'https://schema.org';
  '@type': 'FAQPage';
  mainEntity: Array<{
    '@type': 'Question';
    name: string;
    acceptedAnswer: {
      '@type': 'Answer';
      text: string;
    };
  }>;
}

export function generateFAQLd(
  faqs: Array<{ question: string; answer: string }>,
): FAQPage {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map((faq) => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer,
      },
    })),
  };
}