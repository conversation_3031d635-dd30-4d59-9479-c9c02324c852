'use client';

import { useState, useRef, useEffect, ReactNode } from 'react';

interface LazyLoadSectionProps {
  children: ReactNode;
  threshold?: number;
  rootMargin?: string;
}

export function LazyLoadSection({ 
  children, 
  threshold = 0.1, 
  rootMargin = '100px 0px' 
}: LazyLoadSectionProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);
  
  useEffect(() => {
    if (!ref.current) return;
    
    const observer = new IntersectionObserver(
      ([entry]) => {
        // When section comes into view
        if (entry.isIntersecting) {
          setIsVisible(true);
          // Once visible, no need to observe anymore
          observer.unobserve(entry.target);
        }
      },
      {
        threshold, // Percentage of the element that needs to be visible
        rootMargin, // Margin around the root (viewport)
      }
    );
    
    observer.observe(ref.current);
    
    return () => {
      if (ref.current) {
        observer.unobserve(ref.current);
      }
    };
  }, [threshold, rootMargin]);
  
  return (
    <div ref={ref} className="min-h-[10px]">
      {isVisible ? children : null}
    </div>
  );
} 