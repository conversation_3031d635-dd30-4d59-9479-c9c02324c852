'use client';

import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Logo } from './logo';

interface PreloaderProps {
  isLoading: boolean;
  onLoadingComplete?: () => void;
  minimumLoadingTime?: number;
}

export function Preloader({
  isLoading: initialIsLoading = true,
  onLoadingComplete,
  minimumLoadingTime = 4000, // Significantly increased to 4 seconds minimum display time
}: PreloaderProps) {
  const [isLoading, setIsLoading] = useState(initialIsLoading);
  const [contentReady, setContentReady] = useState(false);
  const [isBrowser, setIsBrowser] = useState(false);

  // Set isBrowser to true once component mounts
  useEffect(() => {
    setIsBrowser(true);
  }, []);

  useEffect(() => {
    // Skip during server-side rendering
    if (!isBrowser) return;
    
    if (!initialIsLoading) {
      // If initial loading is false, start fade out sequence
      const fadeOutTimer = setTimeout(() => {
        setIsLoading(false);
        if (onLoadingComplete) {
          onLoadingComplete();
        }
      }, 800); // Increased delay before starting fade out
      
      return () => clearTimeout(fadeOutTimer);
    } else {
      // For initial load, show preloader for minimum time
      setIsLoading(true);
      
      // Set content ready after a delay to trigger animations
      const contentTimer = setTimeout(() => {
        setContentReady(true);
      }, 600); // Increased from 400ms to 600ms
      
      // Ensure the preloader shows for at least the minimum time
      const minTimeTimer = setTimeout(() => {
        setIsLoading(false);
        if (onLoadingComplete) {
          onLoadingComplete();
        }
      }, minimumLoadingTime);
      
      return () => {
        clearTimeout(contentTimer);
        clearTimeout(minTimeTimer);
      };
    }
  }, [initialIsLoading, minimumLoadingTime, onLoadingComplete, isBrowser]);

  // Don't render anything during server-side rendering
  if (!isBrowser) {
    return null;
  }

  return (
    <AnimatePresence mode="wait">
      {isLoading && (
        <motion.div
          className="fixed inset-0 z-50 flex flex-col items-center justify-center bg-background"
          initial={{ opacity: 1 }}
          exit={{ 
            opacity: 0,
            transition: { 
              duration: 1.2, // Increased from 0.8s to 1.2s
              ease: "easeInOut" 
            }
          }}
        >
          <div className="flex flex-col items-center justify-center">
            {/* Logo */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ 
                opacity: contentReady ? 1 : 0, 
                y: contentReady ? 0 : 20 
              }}
              transition={{ duration: 1.0, delay: 0.3 }} // Increased duration and delay
              className="mb-8"
            >
              <Logo width={240} height={78} />
            </motion.div>
            
            {/* Loading animation */}
            <motion.div
              className="flex space-x-4 justify-center items-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: contentReady ? 1 : 0 }}
              transition={{ duration: 1.0, delay: 0.6 }} // Increased duration and delay
            >
              <motion.div
                className="w-4 h-4 rounded-full bg-primary"
                animate={{
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 1.8, // Slowed down the animation
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <motion.div
                className="w-4 h-4 rounded-full bg-secondary"
                animate={{
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 1.8, // Slowed down the animation
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.2,
                }}
              />
              <motion.div
                className="w-4 h-4 rounded-full bg-primary"
                animate={{
                  scale: [1, 1.5, 1],
                }}
                transition={{
                  duration: 1.8, // Slowed down the animation
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.4,
                }}
              />
            </motion.div>
            
            {/* Loading text */}
            <motion.p
              className="mt-4 text-foreground/60 text-sm"
              initial={{ opacity: 0 }}
              animate={{ opacity: contentReady ? 1 : 0 }}
              transition={{ duration: 1.0, delay: 0.9 }} // Increased duration and delay
            >
              Loading Advisync Solutions...
            </motion.p>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
} 