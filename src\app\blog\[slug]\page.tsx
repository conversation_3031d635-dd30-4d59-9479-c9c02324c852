import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { Facebook, Twitter, Linkedin, Calendar, ArrowLeft, ArrowRight } from 'lucide-react';
import { OptimizedImage } from '@/components/common/optimized-image';
import { Button } from '@/components/common/button';
import { Container } from '@/components/common/container';
import { NewsletterForm } from '@/components/common/newsletter-form';
import { blogPosts } from '@/config/blog-posts';
import { Clock, Tag } from 'lucide-react';

interface BlogPostPageProps {
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostPageProps): Promise<Metadata> {
  const post = blogPosts.find(p => p.id === params.slug);
  
  if (!post) {
    return {
      title: 'Blog Post Not Found | Advisync Solutions',
      description: 'The requested blog post could not be found.'
    };
  }

  // Use custom SEO metadata if available, otherwise use defaults
  return {
    title: post.seoMetadata?.title || `${post.title} | Advisync Solutions Blog`,
    description: post.seoMetadata?.description || post.description,
    keywords: post.seoMetadata?.keywords || [
      ...post.tags,
      'Melbourne business automation',
      'digital transformation Victoria',
      'automation insights',
      'small business technology blog'
    ],
    alternates: {
      canonical: post.seoMetadata?.canonicalUrl || `https://advisync.com.au/blog/${post.id}`
    },
    openGraph: {
      title: post.seoMetadata?.title || post.title,
      description: post.seoMetadata?.description || post.description,
      url: `https://advisync.com.au/blog/${post.id}`,
      type: 'article',
      publishedTime: post.date,
      modifiedTime: post.lastUpdated || post.date,
      authors: [post.author.name],
      images: [
        {
          url: post.image,
          width: 1200,
          height: 630,
          alt: post.title
        }
      ]
    }
  };
}

export default function BlogPostPage({ params }: BlogPostPageProps) {
  const post = blogPosts.find(p => p.id === params.slug);
  
  if (!post) {
    notFound();
  }

  // Ensure content is a string for JSON-LD
  const safeContent = typeof post.content === 'string' ? post.content : '';

  // Use custom schema if available, otherwise generate default
  const jsonLd = post.seoMetadata?.schema || {
    '@context': 'https://schema.org',
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.description,
    image: post.image,
    datePublished: post.date,
    dateModified: post.lastUpdated || post.date,
    author: {
      '@type': 'Person',
      name: post.author.name,
      description: post.author.bio || undefined,
      image: post.author.image || undefined
    },
    publisher: {
      '@type': 'Organization',
      name: 'Advisync Solutions',
      logo: {
        '@type': 'ImageObject',
        url: 'https://advisync.com.au/images/advisync-logo.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://advisync.com.au/blog/${post.id}`
    },
    keywords: post.tags.join(', '),
    articleBody: safeContent,
    locationCreated: {
      '@type': 'Place',
      name: 'Melbourne, Victoria, Australia'
    }
  };

  const currentIndex = blogPosts.findIndex(p => p.id === post.id);
  const prevPost = currentIndex > 0 ? blogPosts[currentIndex - 1] : null;
  const nextPost = currentIndex < blogPosts.length - 1 ? blogPosts[currentIndex + 1] : null;

  return (
    <main className="py-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <Container>
        <Link
          href="/blog"
          className="inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors mb-12"
        >
          <ArrowLeft className="w-4 h-4" />
          <span>Back to Blog</span>
        </Link>

        <article className="max-w-4xl mx-auto">
          <header className="mb-12">
            <div className="flex items-center gap-4 text-sm text-foreground/40 mb-4">
              <span className="flex items-center gap-1">
                <Clock className="w-4 h-4" />
                {post.readTime}
              </span>
              <span className="flex items-center gap-1">
                <Tag className="w-4 h-4" />
                {post.category}
              </span>
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">{post.title}</h1>
            <p className="text-xl text-foreground/60 mb-8">{post.description}</p>
            <div className="flex flex-wrap gap-2 mb-8">
              {post.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-3 py-1 bg-primary/10 rounded-full text-sm text-primary"
                >
                  {tag}
                </span>
              ))}
            </div>
            
            {/* Social Sharing */}
            <div className="flex flex-wrap items-center gap-4 mb-8">
              <span className="text-foreground/60 text-sm font-medium">Share this article:</span>
              <div className="flex gap-2">
                <a
                  href={`https://www.facebook.com/sharer/sharer.php?u=https://advisync.com.au/blog/${post.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Share on Facebook"
                  className="w-9 h-9 flex items-center justify-center rounded-full bg-blue-600 text-white hover:bg-blue-700 transition-colors"
                >
                  <Facebook size={18} />
                </a>
                <a
                  href={`https://twitter.com/intent/tweet?url=https://advisync.com.au/blog/${post.id}&text=${encodeURIComponent(post.title)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Share on Twitter"
                  className="w-9 h-9 flex items-center justify-center rounded-full bg-black text-white hover:bg-gray-800 transition-colors"
                >
                  <Twitter size={18} />
                </a>
                <a
                  href={`https://www.linkedin.com/shareArticle?mini=true&url=https://advisync.com.au/blog/${post.id}&title=${encodeURIComponent(post.title)}&summary=${encodeURIComponent(post.description)}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label="Share on LinkedIn"
                  className="w-9 h-9 flex items-center justify-center rounded-full bg-blue-700 text-white hover:bg-blue-800 transition-colors"
                >
                  <Linkedin size={18} />
                </a>
              </div>
            </div>
          </header>

          <div className="relative aspect-video rounded-xl overflow-hidden mb-12">
            <OptimizedImage
              src={post.image}
              alt={post.title}
              priority={true}
              className="aspect-video"
              width={800}
              height={450}
            />
          </div>

          <div 
            className="prose prose-lg prose-invert max-w-none"
            dangerouslySetInnerHTML={{ __html: post.content || '' }}
          />

          <footer className="mt-16 pt-8 border-t border-primary/10">
            <div className="flex items-center gap-6">
              {post.author.image && (
                <div className="relative w-16 h-16 rounded-full overflow-hidden">
                  <OptimizedImage
                    src={post.author.image}
                    alt={post.author.name}
                    className="object-cover"
                    width={64}
                    height={64}
                  />
                </div>
              )}
              <div>
                <h3 className="text-lg font-semibold">{post.author.name}</h3>
                <p className="text-foreground/60">{post.author.role}</p>
                {post.author.bio && (
                  <p className="text-foreground/60 mt-2">{post.author.bio}</p>
                )}
              </div>
            </div>
          </footer>

          <nav className="flex justify-between items-center border-t border-border/50 pt-8 mb-16">
            {prevPost ? (
              <Link
                href={`/blog/${prevPost.id}`}
                className="group flex items-center gap-2 text-foreground/60 hover:text-primary transition-colors"
              >
                <ArrowLeft className="w-4 h-4 transition-transform group-hover:-translate-x-1" />
                <div>
                  <div className="text-sm">Previous</div>
                  <div className="font-medium">{prevPost.title}</div>
                </div>
              </Link>
            ) : (
              <div />
            )}
            {nextPost ? (
              <Link
                href={`/blog/${nextPost.id}`}
                className="group flex items-center gap-2 text-right text-foreground/60 hover:text-primary transition-colors"
              >
                <div>
                  <div className="text-sm">Next</div>
                  <div className="font-medium">{nextPost.title}</div>
                </div>
                <ArrowRight className="w-4 h-4 transition-transform group-hover:translate-x-1" />
              </Link>
            ) : (
              <div />
            )}
          </nav>
          
          {/* Newsletter Section */}
          <div className="mt-16">
            <NewsletterForm 
              title="Subscribe for More Insights"
              description="Get the latest digital transformation and business automation tips for Melbourne businesses delivered to your inbox."
            />
          </div>
        </article>
      </Container>
    </main>
  );
}

export async function generateStaticParams() {
  return blogPosts.map((post) => ({
    slug: post.id,
  }));
} 