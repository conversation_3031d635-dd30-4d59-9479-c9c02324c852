import { ArrowRight, CheckCircle2, Clock, Calendar, MessageSquare } from 'lucide-react';
import { Container } from '@/components/common/container';
import Link from 'next/link';
import { Button } from '@/components/common/button';

const benefits = [
  {
    icon: Clock,
    title: 'Quick Response',
    description: 'Get a response within 24 hours'
  },
  {
    icon: Calendar,
    title: 'Flexible Scheduling',
    description: 'Choose a time that suits you'
  },
  {
    icon: MessageSquare,
    title: 'Expert Consultation',
    description: 'Speak with our automation specialists'
  },
  {
    icon: CheckCircle2,
    title: 'No Obligation',
    description: 'Free consultation with no commitments'
  }
];

export function ConsultationCTA() {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 relative">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
      </div>

      <Container>
        <div className="relative px-4 sm:px-6 md:px-0">
          {/* Content */}
          <div className="text-center mb-8 md:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 md:mb-6 bg-clip-text text-transparent bg-gradient-to-r from-foreground to-accent-secondary">
              Ready to Transform Your Business?
            </h2>
            <p className="text-base sm:text-lg md:text-xl text-foreground/60 max-w-3xl mx-auto">
              Book a free consultation with our automation experts and discover how we can help streamline your operations and boost efficiency.
            </p>
          </div>

          {/* Benefits Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 md:gap-8 mb-8 md:mb-12">
            {benefits.map((benefit) => (
              <div
                key={benefit.title}
                className="bg-secondary/5 backdrop-blur-sm rounded-xl md:rounded-2xl p-4 md:p-6 text-center"
              >
                <div className="inline-flex items-center justify-center w-10 h-10 md:w-12 md:h-12 rounded-full bg-accent-secondary/10 mb-3 md:mb-4">
                  <benefit.icon className="w-5 h-5 md:w-6 md:h-6 text-accent-secondary" />
                </div>
                <h3 className="text-base md:text-lg font-semibold mb-1 md:mb-2">{benefit.title}</h3>
                <p className="text-sm md:text-base text-foreground/60">{benefit.description}</p>
              </div>
            ))}
          </div>

          {/* CTA Button */}
          <div className="flex flex-col items-center">
            <Button 
              asChild
              size="responsive-lg"
              className="group rounded-full"
            >
              <Link href="/consultation">
                <span>Free Consultation</span>
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2 transform transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
            <p className="mt-3 md:mt-4 text-sm md:text-base text-foreground/60 text-center px-4">
              No obligations, no pressure - just practical solutions for your business
            </p>
          </div>
        </div>
      </Container>
    </section>
  );
} 