const fs = require('fs');
const path = require('path');
const https = require('https');
const sharp = require('sharp');

const UNSPLASH_ACCESS_KEY = process.env.UNSPLASH_ACCESS_KEY;
const BLOG_IMAGE_DIRECTORY = path.join(process.cwd(), 'public/images/blog');

const blogImages = [
  {
    query: 'business automation technology modern office',
    filename: 'automation-tools-2024.jpg',
    width: 1200,
    height: 630,
    alt: 'Modern business automation tools and technology'
  },
  {
    query: 'zapier automation workflow diagram',
    filename: 'zapier-automation.jpg',
    width: 800,
    height: 600,
    alt: 'Zapier automation workflow example'
  },
  {
    query: 'n8n workflow automation interface',
    filename: 'n8n-workflow.jpg',
    width: 800,
    height: 600,
    alt: 'n8n workflow interface'
  },
  {
    query: 'ai marketing digital strategy',
    filename: 'ai-marketing.jpg',
    width: 800,
    height: 600,
    alt: 'AI-powered marketing strategies visualization'
  },
  {
    query: 'digital transformation business technology',
    filename: 'digital-transformation.jpg',
    width: 800,
    height: 600,
    alt: 'Digital transformation and modern business'
  },
  {
    query: 'team profile professional business',
    filename: 'alex.jpg',
    width: 400,
    height: 400,
    alt: 'Alex Thompson - Head of Automation'
  }
];

async function downloadImage(url, filepath) {
  return new Promise((resolve, reject) => {
    https.get(url, (response) => {
      if (response.statusCode === 200) {
        const writeStream = fs.createWriteStream(filepath);
        response.pipe(writeStream);
        writeStream.on('finish', () => {
          writeStream.close();
          resolve();
        });
      } else {
        reject(new Error(`Failed to download image: ${response.statusCode}`));
      }
    }).on('error', reject);
  });
}

async function optimizeImage(filepath, width, height) {
  const options = {
    fit: 'cover',
    position: 'center',
    width: width,
    height: height
  };

  try {
    // Create optimized JPEG
    await sharp(filepath)
      .resize(options)
      .jpeg({
        quality: 85,
        progressive: true,
        chromaSubsampling: '4:4:4'
      })
      .toFile(`${filepath}.optimized`);

    fs.unlinkSync(filepath);
    fs.renameSync(`${filepath}.optimized`, filepath);

    // Create WebP version
    await sharp(filepath)
      .resize(options)
      .webp({
        quality: 85,
        effort: 6
      })
      .toFile(filepath.replace('.jpg', '.webp'));

    console.log(`Successfully optimized ${filepath}`);
  } catch (error) {
    console.error(`Error optimizing ${filepath}:`, error);
    throw error;
  }
}

async function ensureDirectoryExists(directory) {
  if (!fs.existsSync(directory)) {
    fs.mkdirSync(directory, { recursive: true });
  }
}

async function processImages(images, directory) {
  for (const image of images) {
    const filepath = path.join(directory, image.filename);
    
    try {
      // Download image from Unsplash
      const response = await fetch(
        `https://api.unsplash.com/photos/random?query=${encodeURIComponent(image.query)}&orientation=landscape`,
        {
          headers: {
            'Authorization': `Client-ID ${UNSPLASH_ACCESS_KEY}`
          }
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch image URL: ${response.statusText}`);
      }

      const data = await response.json();
      const imageUrl = data.urls.raw;

      console.log(`Downloading ${image.filename}...`);
      await downloadImage(imageUrl, filepath);

      console.log(`Optimizing ${image.filename}...`);
      await optimizeImage(filepath, image.width, image.height);

      console.log(`Successfully processed ${image.filename}`);
    } catch (error) {
      console.error(`Error processing ${image.filename}:`, error);
    }
  }
}

async function main() {
  if (!UNSPLASH_ACCESS_KEY) {
    console.error('Please set UNSPLASH_ACCESS_KEY environment variable');
    process.exit(1);
  }

  // Ensure directory exists
  await ensureDirectoryExists(BLOG_IMAGE_DIRECTORY);

  // Process blog images
  console.log('\nProcessing blog images...');
  await processImages(blogImages, BLOG_IMAGE_DIRECTORY);

  console.log('\nAll blog images have been processed successfully!');
}

main().catch(console.error); 