'use client';

import * as React from 'react';
import { useState } from 'react';
import { motion } from 'framer-motion';
import { Calculator, AlertTriangle } from 'lucide-react';
import { Container } from "@/components/common/container";
import { Button } from '@/components/common/button';

// Currency utility for Australian formatting
const aud = (n: number) => new Intl.NumberFormat('en-AU', {
  style: 'currency',
  currency: 'AUD',
  maximumFractionDigits: 0
}).format(n);

export function SavingsCalculator() {
  // Calculator inputs with realistic defaults for Australian small businesses
  const [callsPerWeek, setCallsPerWeek] = useState(15);
  const [missedCallPercentage, setMissedCallPercentage] = useState(30);
  const [avgJobValue, setAvgJobValue] = useState(325);
  const [conversionRate, setConversionRate] = useState(50);

  // Calculate results
  const missedCallsPerWeek = Math.round((callsPerWeek * missedCallPercentage) / 100);
  const lostCustomersPerWeek = Math.round((missedCallsPerWeek * conversionRate) / 100);
  const lostRevenuePerWeek = lostCustomersPerWeek * avgJobValue;
  const potentialLostRevenuePerYear = lostRevenuePerWeek * 52;

  // Dynamic messaging based on calculated values
  const getDynamicMessage = () => {
    if (potentialLostRevenuePerYear === 0) {
      return {
        title: "Perfect! You're not missing any opportunities",
        subtitle: "But what if your business grows?",
        description: "As your business expands and call volume increases, having an AI voice agent ensures you'll never miss a potential customer - even during your busiest periods.",
        buttonText: "Prepare for Growth"
      };
    } else if (potentialLostRevenuePerYear < 5000) {
      return {
        title: `You could be missing ${aud(potentialLostRevenuePerYear)} in opportunities annually`,
        subtitle: "Small leaks sink big ships",
        description: "Even small amounts of missed revenue add up over time. Our AI voice agents ensure you capture every opportunity, no matter how busy you get.",
        buttonText: "Stop Missing Opportunities"
      };
    } else if (potentialLostRevenuePerYear < 20000) {
      return {
        title: `Can you afford to lose ${aud(potentialLostRevenuePerYear)} annually?`,
        subtitle: "That's significant revenue walking away",
        description: "Our AI voice agents answer every call, book appointments, and never miss a potential customer - even when you're busy on the job.",
        buttonText: "Start Saving These Opportunities"
      };
    } else {
      return {
        title: `You're potentially losing ${aud(potentialLostRevenuePerYear)} every year!`,
        subtitle: "This is a serious revenue leak",
        description: "With this level of missed opportunities, an AI voice agent isn't just helpful - it's essential for your business growth. Stop letting money walk away.",
        buttonText: "Stop This Revenue Loss Now"
      };
    }
  };

  const dynamicMessage = getDynamicMessage();

  return (
    <section className="py-16 md:py-24 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-primary">
      <Container>
        <div className="text-center mb-12">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-accent-primary/20 border border-accent-primary/30"
          >
            <Calculator className="w-5 h-5 text-accent-primary" />
            <span className="text-sm font-medium text-text-heading">Savings Calculator</span>
          </motion.div>

          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold text-text-heading mb-4"
          >
            Calculate Your Lost Revenue
          </motion.h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-lg text-text-body max-w-3xl mx-auto"
          >
            See how much money missed calls could be costing your business every year. Adjust the sliders below to match your situation.
          </motion.p>
        </div>

        <div className="max-w-6xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-8">
            {/* Left Side - Business Details */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              className="bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 rounded-lg bg-accent-primary/20 flex items-center justify-center">
                  <span className="text-accent-primary text-sm">📊</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-text-heading">Your Business Details</h3>
                  <p className="text-sm text-text-body">Adjust these values to match your business</p>
                </div>
              </div>

              <div className="space-y-8">
                {/* Calls You Receive per Week */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="text-sm font-medium text-text-heading">Calls You Receive per Week</label>
                    <span className="text-lg font-bold text-accent-secondary">{callsPerWeek} calls</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="100"
                    value={callsPerWeek}
                    onChange={(e) => setCallsPerWeek(Number(e.target.value))}
                    className="slider w-full"
                  />
                  <p className="text-xs text-text-body mt-2">Industry average: 15-25 calls per week for small trade businesses</p>
                </div>

                {/* Percentage of Calls You Miss */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="text-sm font-medium text-text-heading">Percentage of Calls You Miss</label>
                    <span className="text-lg font-bold text-accent-primary">{missedCallPercentage}%</span>
                  </div>
                  <input
                    type="range"
                    min="0"
                    max="80"
                    value={missedCallPercentage}
                    onChange={(e) => setMissedCallPercentage(Number(e.target.value))}
                    className="slider w-full"
                  />
                  <p className="text-xs text-text-body mt-2">Most businesses miss 20-40% of calls when busy on jobs</p>
                </div>

                {/* Average Value per New Job */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="text-sm font-medium text-text-heading">Average Value per New Job (AUD)</label>
                    <span className="text-lg font-bold text-accent-secondary">{aud(avgJobValue)}</span>
                  </div>
                  <input
                    type="range"
                    min="100"
                    max="2000"
                    step="25"
                    value={avgJobValue}
                    onChange={(e) => setAvgJobValue(Number(e.target.value))}
                    className="slider w-full"
                  />
                  <p className="text-xs text-text-body mt-2">Consider your typical job value - plumbing, electrical, HVAC, etc.</p>
                </div>

                {/* Call to Customer Conversion Rate */}
                <div>
                  <div className="flex justify-between items-center mb-3">
                    <label className="text-sm font-medium text-text-heading">Call to Customer Conversion Rate</label>
                    <span className="text-lg font-bold text-accent-primary">{conversionRate}%</span>
                  </div>
                  <input
                    type="range"
                    min="20"
                    max="80"
                    value={conversionRate}
                    onChange={(e) => setConversionRate(Number(e.target.value))}
                    className="slider w-full"
                  />
                  <p className="text-xs text-text-body mt-2">What percentage of callers become paying customers</p>
                </div>
              </div>
            </motion.div>

            {/* Right Side - Lost Revenue */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="bg-gradient-to-br from-red-500/10 to-red-600/5 backdrop-blur-sm rounded-2xl p-8 border border-red-500/20"
            >
              <div className="flex items-center gap-3 mb-6">
                <div className="w-8 h-8 rounded-lg bg-red-500/20 flex items-center justify-center">
                  <span className="text-red-500 text-sm">�</span>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-text-heading">
                    {potentialLostRevenuePerYear === 0
                      ? 'Your Business Performance'
                      : 'Your Potential Lost Revenue'
                    }
                  </h3>
                  <p className="text-sm text-text-body">
                    {potentialLostRevenuePerYear === 0
                      ? 'Great job! Here\'s your current status'
                      : 'Based on your inputs above'
                    }
                  </p>
                </div>
              </div>

              <div className="space-y-6">
                <div className={`text-center p-6 rounded-xl border ${
                  potentialLostRevenuePerYear === 0
                    ? 'bg-green-500/10 border-green-500/20'
                    : potentialLostRevenuePerYear < 5000
                    ? 'bg-yellow-500/10 border-yellow-500/20'
                    : 'bg-red-500/10 border-red-500/20'
                }`}>
                  <div className={`text-5xl font-bold mb-2 ${
                    potentialLostRevenuePerYear === 0
                      ? 'text-green-500'
                      : potentialLostRevenuePerYear < 5000
                      ? 'text-yellow-500'
                      : 'text-red-500'
                  }`}>
                    {potentialLostRevenuePerYear === 0 ? '✓' : aud(potentialLostRevenuePerYear)}
                  </div>
                  <div className="text-lg font-medium text-text-heading mb-1">
                    {potentialLostRevenuePerYear === 0
                      ? 'No revenue lost - excellent!'
                      : 'Potential revenue lost per year'
                    }
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className={`text-2xl font-bold mb-1 ${
                      missedCallsPerWeek === 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {missedCallsPerWeek === 0 ? '0 ✓' : missedCallsPerWeek}
                    </div>
                    <div className="text-sm text-text-body">
                      {missedCallsPerWeek === 0 ? 'Perfect call handling!' : 'Missed calls per week'}
                    </div>
                  </div>
                  <div className="text-center p-4 bg-white/5 rounded-lg">
                    <div className={`text-2xl font-bold mb-1 ${
                      lostCustomersPerWeek === 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {lostCustomersPerWeek === 0 ? '0 ✓' : lostCustomersPerWeek}
                    </div>
                    <div className="text-sm text-text-body">
                      {lostCustomersPerWeek === 0 ? 'No lost customers!' : 'Lost customers per week'}
                    </div>
                  </div>
                </div>

                <div className="text-center p-4 bg-white/5 rounded-lg">
                  <div className={`text-2xl font-bold mb-1 ${
                    lostRevenuePerWeek === 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {lostRevenuePerWeek === 0 ? '✓ $0' : aud(lostRevenuePerWeek)}
                  </div>
                  <div className="text-sm text-text-body">
                    {lostRevenuePerWeek === 0 ? 'No revenue lost!' : 'Lost revenue per week'}
                  </div>
                </div>

                {/* Dynamic Industry Insight */}
                <div className={`rounded-lg p-4 ${
                  potentialLostRevenuePerYear === 0
                    ? 'bg-blue-500/10 border border-blue-500/20'
                    : 'bg-yellow-500/10 border border-yellow-500/20'
                }`}>
                  <div className="flex items-start gap-3">
                    {potentialLostRevenuePerYear === 0 ? (
                      <div className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0">💡</div>
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5 flex-shrink-0" />
                    )}
                    <div>
                      <h4 className={`font-semibold mb-1 ${
                        potentialLostRevenuePerYear === 0 ? 'text-blue-500' : 'text-yellow-500'
                      }`}>
                        {potentialLostRevenuePerYear === 0 ? 'Pro Tip for Growth' : 'Industry Reality Check'}
                      </h4>
                      <p className="text-sm text-text-body">
                        {potentialLostRevenuePerYear === 0
                          ? 'As your business grows, call volume will increase. Having an AI voice agent ready ensures you can scale without missing opportunities during busy periods.'
                          : '80% of customers won\'t call back if they reach your voicemail the first time. Every missed call is a potential lost customer.'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          </div>

          {/* Bottom CTA - Dynamic based on calculated values */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.3 }}
            className="mt-12 text-center"
          >
            <div className={`backdrop-blur-sm rounded-2xl p-8 border ${
              potentialLostRevenuePerYear === 0
                ? 'bg-gradient-to-br from-green-500/10 to-green-600/10 border-green-500/20'
                : potentialLostRevenuePerYear < 5000
                ? 'bg-gradient-to-br from-yellow-500/10 to-yellow-600/10 border-yellow-500/20'
                : 'bg-gradient-to-br from-red-500/10 to-red-600/10 border-red-500/20'
            }`}>
              <div className="text-2xl font-bold text-text-heading mb-2">
                {dynamicMessage.title}
              </div>
              {dynamicMessage.subtitle && (
                <div className="text-lg font-medium text-accent-secondary mb-4">
                  {dynamicMessage.subtitle}
                </div>
              )}
              <p className="text-text-body mb-6 max-w-2xl mx-auto">
                {dynamicMessage.description}
              </p>
              <Button className="bg-accent-primary hover:bg-accent-secondary text-white px-8 py-3 text-lg">
                {dynamicMessage.buttonText}
              </Button>
            </div>
          </motion.div>
        </div>
      </Container>

      <style jsx>{`
        .slider {
          -webkit-appearance: none;
          appearance: none;
          height: 8px;
          border-radius: 4px;
          background: rgba(255, 255, 255, 0.1);
          outline: none;
          transition: all 0.3s ease;
        }

        .slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #1A6BFF;
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 8px rgba(26, 107, 255, 0.3);
          transition: all 0.2s ease;
        }

        .slider::-webkit-slider-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(26, 107, 255, 0.5);
        }

        .slider::-moz-range-thumb {
          width: 20px;
          height: 20px;
          border-radius: 50%;
          background: #1A6BFF;
          cursor: pointer;
          border: 2px solid white;
          box-shadow: 0 2px 8px rgba(26, 107, 255, 0.3);
          transition: all 0.2s ease;
        }

        .slider::-moz-range-thumb:hover {
          transform: scale(1.1);
          box-shadow: 0 4px 12px rgba(26, 107, 255, 0.5);
        }
      `}</style>
    </section>
  );
}

export default SavingsCalculator;
