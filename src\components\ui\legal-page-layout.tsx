import React from 'react';
import { Container } from '@/components/common/container';
import { PageHeader } from '@/components/ui/page-header';

interface LegalPageLayoutProps {
  title: string;
  lastUpdated: string;
  children: React.ReactNode;
}

export function LegalPageLayout({ title, lastUpdated, children }: LegalPageLayoutProps) {
  return (
    <main className="flex-1">
      <PageHeader title={title} />
      
      <Container className="py-12">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8 pb-8 border-b border-border/50">
            <p className="text-foreground/60 text-sm">
              Last updated: {lastUpdated}
            </p>
          </div>
          
          <div className="prose prose-invert prose-primary max-w-none">
            {children}
          </div>
          
          <div className="mt-12 pt-8 border-t border-border/50">
            <p className="text-foreground/60 text-sm">
              If you have any questions about this {title.toLowerCase()}, please contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-primary hover:underline">
                <EMAIL>
              </a>
            </p>
          </div>
        </div>
      </Container>
    </main>
  );
} 