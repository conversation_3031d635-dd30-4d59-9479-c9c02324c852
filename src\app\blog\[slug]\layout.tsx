import type { Metadata } from 'next';
import { blogPosts } from '@/config/blog-posts';

interface BlogPostLayoutProps {
  children: React.ReactNode;
  params: {
    slug: string;
  };
}

export async function generateMetadata({ params }: BlogPostLayoutProps): Promise<Metadata> {
  const post = blogPosts.find(post => post.id === params.slug);

  if (!post) {
    return {
      title: 'Blog Post Not Found - Advisync AI Solutions',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: `${post.title} - Advisync AI Solutions Blog`,
    description: post.description,
    keywords: [
      ...post.tags,
      'Advisync AI Solutions',
      'Melbourne business automation',
      'digital transformation',
      'small business solutions',
      'NDIS automation'
    ],
    authors: [{ name: post.author.name, url: '/about' }],
    openGraph: {
      title: post.title,
      description: post.description,
      type: 'article',
      url: `https://advisync.com.au/blog/${post.id}`,
      images: [post.image],
      publishedTime: post.date,
      authors: [post.author.name],
      tags: post.tags,
      siteName: 'Advisync AI Solutions'
    },
    twitter: {
      card: 'summary_large_image',
      title: post.title,
      description: post.description,
      images: [post.image],
      creator: '@advisync'
    },
  };
}

export default function BlogPostLayout({ children }: BlogPostLayoutProps) {
  return children;
}