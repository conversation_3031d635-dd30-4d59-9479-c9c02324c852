'use client';

import * as React from 'react';
import { motion } from 'framer-motion';
import { Arrow<PERSON><PERSON>, Sparkles, Bot, Zap, Boxes, Users } from 'lucide-react';
import { Container } from "@/components/common/container";
import { Button } from '@/components/common/button';
import { OptimizedImage } from '@/components/common/optimized-image';
import { fadeInUp, floatingAnimation } from './animations';
import Link from 'next/link';
export function HeroSection() {

  return (
    <section className="relative min-h-[calc(100vh-4rem)] flex items-center justify-center overflow-hidden bg-background py-12 sm:py-16 md:py-20">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-background/90 z-10" />
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_center,_var(--tw-gradient-stops))] from-primary/10 via-background to-background" />
        <div className="absolute inset-0 bg-grid" />
      </div>

      {/* Content */}
      <Container className="relative z-30">
        <div className="grid lg:grid-cols-12 gap-8 md:gap-12 items-center py-8 md:py-16">
          {/* Left Content - Takes up more space */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="lg:col-span-7 text-center lg:text-left relative"
          >
            {/* Trust Badge */}
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, duration: 0.6 }}
              className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-gradient-to-r from-accent-primary/20 to-accent-secondary/20 border border-accent-primary/30 backdrop-blur-sm"
            >
              <div className="w-2 h-2 bg-success rounded-full animate-pulse" />
              <span className="text-sm font-medium text-text-heading">Trusted by 50+ Australian Businesses</span>
            </motion.div>

            {/* Main Heading - More impactful */}
            <motion.h1
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.8 }}
              className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold mb-6 tracking-tight leading-[1.1] text-text-heading"
            >
              Your AI-Powered Business Assistant
            </motion.h1>

            {/* Value Proposition - Clear and compelling */}
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.8 }}
              className="text-lg sm:text-xl md:text-2xl text-text-body mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed font-light"
            >
              Never miss a call again. Our AI voice agents provide
              <span className="text-accent-primary font-medium"> 24/7 customer service</span>,
              <span className="text-accent-secondary font-medium"> automate workflows</span>, and
              <span className="text-accent-primary font-medium"> boost your revenue</span> while you sleep.
            </motion.p>

            {/* Key Benefits - Visual impact */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6, duration: 0.8 }}
              className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8"
            >
              {[
                { icon: Bot, label: "AI Voice Agents", desc: "Human-like conversations" },
                { icon: Zap, label: "Instant Response", desc: "Zero wait times" },
                { icon: Users, label: "24/7 Available", desc: "Never miss a lead" }
              ].map((benefit, index) => (
                <motion.div
                  key={benefit.label}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.7 + index * 0.1, duration: 0.6 }}
                  className="flex flex-col items-center lg:items-start text-center lg:text-left p-4 rounded-2xl bg-surface-card/50 backdrop-blur-sm border border-border-hair hover:border-accent-primary/30 transition-all duration-300 group"
                >
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-accent-primary/20 to-accent-secondary/20 flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300">
                    <benefit.icon className="w-6 h-6 text-accent-primary" />
                  </div>
                  <h3 className="font-semibold text-text-heading mb-1">{benefit.label}</h3>
                  <p className="text-sm text-text-body">{benefit.desc}</p>
                </motion.div>
              ))}
            </motion.div>

            {/* CTA Buttons - More prominent and modern */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.9, duration: 0.8 }}
              className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8"
            >
              <Button asChild size="lg" className="bg-accent-secondary hover:bg-accent-secondary/90 text-bg-primary hover:text-bg-primary font-semibold px-8 py-4 shadow-lg hover:shadow-glow-gold transition-all duration-300">
                <Link href="/consultation" className="flex items-center justify-center gap-3">
                  <span>Book Free Consultation</span>
                  <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="border-2 border-border-hair text-text-heading hover:bg-surface-card hover:border-accent-primary/50 font-semibold px-8 py-4 backdrop-blur-sm transition-all duration-300">
                <Link href="/demo" className="flex items-center justify-center gap-3">
                  <Bot className="w-5 h-5" />
                  Try Voice Demo
                </Link>
              </Button>
            </motion.div>

            {/* Social Proof */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.0, duration: 0.8 }}
              className="flex flex-col sm:flex-row items-center justify-center lg:justify-start gap-6 text-text-body"
            >
              <div className="flex items-center gap-2">
                <div className="flex -space-x-2">
                  {[1, 2, 3, 4].map((i) => (
                    <div key={i} className="w-8 h-8 rounded-full bg-accent-primary/30 border-2 border-border-hair flex items-center justify-center">
                      <Users className="w-4 h-4 text-text-heading" />
                    </div>
                  ))}
                </div>
                <span className="text-sm">50+ Happy Clients</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="flex text-accent-secondary">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <Sparkles key={i} className="w-4 h-4 fill-current" />
                  ))}
                </div>
                <span className="text-sm">4.9/5 Rating</span>
              </div>
            </motion.div>
          </motion.div>

          {/* Right Content - Modern Interactive Dashboard */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4, duration: 0.8 }}
            className="lg:col-span-5 relative mt-12 lg:mt-0"
          >
            {/* Main Dashboard Container */}
            <div className="relative">
              {/* Background Glow */}
              <div className="absolute inset-0 bg-accent-primary/20 rounded-3xl blur-2xl animate-pulse" />

              {/* Dashboard Card */}
              <div className="relative bg-surface-card/80 backdrop-blur-xl border border-border-hair rounded-3xl p-6 md:p-8 shadow-2xl">
                {/* Header */}
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-3">
                    <div className="w-3 h-3 bg-success rounded-full animate-pulse" />
                    <span className="text-text-heading font-medium">AI Assistant Active</span>
                  </div>
                  <div className="text-text-body text-sm">24/7 Online</div>
                </div>

                {/* Live Stats */}
                <div className="grid grid-cols-2 gap-4 mb-6">
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.6, duration: 0.6 }}
                    className="bg-surface-card/50 rounded-2xl p-4 border border-border-hair"
                  >
                    <div className="text-2xl font-bold text-accent-primary mb-1">47</div>
                    <div className="text-text-body text-sm">Calls Today</div>
                  </motion.div>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.7, duration: 0.6 }}
                    className="bg-surface-card/50 rounded-2xl p-4 border border-border-hair"
                  >
                    <div className="text-2xl font-bold text-accent-secondary mb-1">98%</div>
                    <div className="text-text-body text-sm">Satisfaction</div>
                  </motion.div>
                </div>

                {/* Recent Activity */}
                <div className="space-y-3">
                  <h3 className="text-text-heading font-medium mb-3">Recent Activity</h3>
                  {[
                    { time: "2 min ago", action: "New lead captured", icon: Users, color: "accent-primary" },
                    { time: "5 min ago", action: "Appointment booked", icon: Bot, color: "accent-secondary" },
                    { time: "8 min ago", action: "Query resolved", icon: Zap, color: "accent-primary" }
                  ].map((activity, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}
                      className="flex items-center gap-3 p-3 bg-surface-card/50 rounded-xl border border-border-hair"
                    >
                      <div className={`w-8 h-8 rounded-lg bg-${activity.color}/20 flex items-center justify-center`}>
                        <activity.icon className={`w-4 h-4 text-${activity.color}`} />
                      </div>
                      <div className="flex-1">
                        <div className="text-text-heading text-sm font-medium">{activity.action}</div>
                        <div className="text-text-body text-xs">{activity.time}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Voice Wave Animation */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1.2, duration: 0.8 }}
                  className="mt-6 flex items-center justify-center gap-1"
                >
                  {[1, 2, 3, 4, 5, 6, 7].map((i) => (
                    <motion.div
                      key={i}
                      className="w-1 bg-accent-primary rounded-full"
                      animate={{
                        height: [8, 24, 8, 16, 8],
                      }}
                      transition={{
                        duration: 1.5,
                        repeat: Infinity,
                        delay: i * 0.1,
                        ease: "easeInOut"
                      }}
                    />
                  ))}
                </motion.div>
                <div className="text-center text-text-body text-sm mt-2">AI Voice Processing...</div>
              </div>

              {/* Floating Elements */}
              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.0, duration: 0.6 }}
                className="absolute -top-4 -right-4 w-16 h-16 bg-accent-secondary rounded-2xl flex items-center justify-center shadow-2xl"
              >
                <Bot className="w-8 h-8 text-bg-primary" />
              </motion.div>

              <motion.div
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: 1.1, duration: 0.6 }}
                className="absolute -bottom-4 -left-4 w-12 h-12 bg-accent-primary rounded-xl flex items-center justify-center shadow-xl"
              >
                <Sparkles className="w-6 h-6 text-text-heading" />
              </motion.div>
            </div>
          </motion.div>
        </div>
      </Container>
    </section>
  );
}