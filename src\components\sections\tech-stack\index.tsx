'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import { 
  Code2,
  Layers,
  Zap,
  Palette,
  Smartphone,
  Monitor,
  Layout,
  Box
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { cn } from '@/lib/utils';

// Register GSAP plugins
if (typeof window !== 'undefined') {
  gsap.registerPlugin(ScrollTrigger);
}

interface Technology {
  name: string;
  description: string;
  icon: React.ElementType;
  category: string;
  expertise: number;
  features?: string[];
}

const technologies: Technology[] = [
  {
    name: "Next.js 14",
    description: "Latest Next.js framework for building modern web applications",
    icon: Code2,
    category: "Core",
    expertise: 95,
    features: [
      "Server Components",
      "App Router",
      "Server Actions",
      "Edge Runtime"
    ]
  },
  {
    name: "React & TypeScript",
    description: "Type-safe React development with modern patterns",
    icon: Code2,
    category: "Core",
    expertise: 95,
    features: [
      "Custom Hooks",
      "Context API",
      "Performance Optimization",
      "Type Safety"
    ]
  },
  {
    name: "Tailwind CSS",
    description: "Utility-first CSS framework for rapid UI development",
    icon: Palette,
    category: "Styling",
    expertise: 95,
    features: [
      "Custom Design System",
      "Responsive Design",
      "Dark Mode",
      "Animation"
    ]
  },
  {
    name: "Framer Motion",
    description: "Production-ready animation library for React",
    icon: Zap,
    category: "Animation",
    expertise: 90,
    features: [
      "Page Transitions",
      "Gesture Animations",
      "Scroll Animations",
      "Layout Animations"
    ]
  },
  {
    name: "GSAP",
    description: "Professional-grade animation for the modern web",
    icon: Zap,
    category: "Animation",
    expertise: 85,
    features: [
      "ScrollTrigger",
      "Complex Animations",
      "Performance",
      "Timeline Control"
    ]
  },
  {
    name: "Responsive Design",
    description: "Mobile-first approach for all screen sizes",
    icon: Smartphone,
    category: "Core",
    expertise: 95,
    features: [
      "Mobile First",
      "Adaptive Layouts",
      "Fluid Typography",
      "Breakpoint Optimization"
    ]
  },
  {
    name: "UI Components",
    description: "Beautifully designed components built with Radix UI and Tailwind",
    icon: Layout,
    category: "UI",
    expertise: 90,
    features: [
      "Accessible Components",
      "Theme Customization",
      "Modern Design",
      "React Hooks"
    ]
  },
  {
    name: "3D Effects",
    description: "Immersive 3D experiences and animations",
    icon: Box,
    category: "Animation",
    expertise: 85,
    features: [
      "Three.js Integration",
      "WebGL Effects",
      "3D Models",
      "Performance Optimization"
    ]
  }
];

const categories = [
  "All",
  "Core",
  "Animation",
  "Styling",
  "UI"
];

function TechCard({ tech }: { tech: Technology }) {
  const [isHovered, setIsHovered] = useState(false);
  const Icon = tech.icon;

  return (
    <motion.div
      className="relative group cursor-pointer"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      whileHover={{ y: -5 }}
    >
      <div className="bg-background/50 border border-border/50 rounded-2xl p-6 transition-colors hover:border-primary/50">
        {/* Icon and Expertise Indicator */}
        <div className="flex items-start justify-between mb-4">
          <div className="inline-flex h-12 w-12 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-primary group-hover:text-white transition-colors">
            <Icon size={24} />
          </div>
          <div className="relative h-1 w-24 bg-primary/20 rounded-full overflow-hidden">
            <motion.div
              className="absolute inset-y-0 left-0 bg-primary"
              initial={{ width: 0 }}
              animate={{ width: `${tech.expertise}%` }}
              transition={{ duration: 1, ease: "easeOut" }}
            />
          </div>
        </div>

        {/* Content */}
        <h3 className="text-lg font-semibold mb-2 group-hover:text-primary transition-colors">
          {tech.name}
        </h3>
        <p className="text-foreground/60 text-sm mb-4">
          {tech.description}
        </p>

        {/* Features */}
        <AnimatePresence>
          {isHovered && tech.features && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="space-y-2"
            >
              {tech.features.map((feature, index) => (
                <motion.div
                  key={feature}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center text-sm text-foreground/60"
                >
                  <div className="w-1 h-1 bg-primary rounded-full mr-2" />
                  {feature}
                </motion.div>
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
  },
};

export function TechStack() {
  const [selectedCategory, setSelectedCategory] = useState("All");

  const filteredTechnologies = selectedCategory === "All"
    ? technologies
    : technologies.filter(tech => tech.category === selectedCategory);

  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
      </div>

      <Container className="relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4"
          >
            Frontend Technologies
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-foreground/60 max-w-2xl mx-auto"
          >
            We leverage cutting-edge frontend technologies to create stunning, 
            performant, and user-friendly web experiences.
          </motion.p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-4 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={cn(
                "px-6 py-2 rounded-full text-sm font-medium transition-colors",
                selectedCategory === category
                  ? "bg-primary text-white"
                  : "bg-primary/10 text-foreground/60 hover:bg-primary/20"
              )}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Tech Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
        >
          {filteredTechnologies.map((tech) => (
            <motion.div
              key={tech.name}
              variants={itemVariants}
            >
              <TechCard tech={tech} />
            </motion.div>
          ))}
        </motion.div>
      </Container>
    </section>
  );
} 