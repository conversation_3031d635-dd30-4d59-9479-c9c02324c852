import { NextResponse } from 'next/server';
import { z } from 'zod';
import { sendNewsletterToN8N } from '@/lib/n8n';

// Validation schema
const newsletterSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
});

export async function POST(request: Request) {
  try {
    const data = await request.json();

    // Validate the data
    const { email } = newsletterSchema.parse(data);

    // Send data to N8N workflow
    const n8nResult = await sendNewsletterToN8N({ email });

    if (!n8nResult.success) {
      console.error('N8N webhook failed:', n8nResult.error);
      // Continue with success response - don't block user experience
    }
    
    return NextResponse.json({
      success: true,
      message: 'Subscription successful',
    });
  } catch (error) {
    console.error('Newsletter subscription error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: 'Failed to process subscription' 
      },
      { status: 500 }
    );
  }
} 