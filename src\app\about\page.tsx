import type { <PERSON>ada<PERSON> } from 'next';
import { Container } from '@/components/common/container';
import {
  Settings,
  ArrowRight,
  Heart,
  Target,
  Users,
  Clock,
  Briefcase,
  Zap,
  CheckCircle,
  Shield,
  Bot,
  Award,
  Compass,
  Layers
} from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';

export const metadata: Metadata = {
  title: 'About Advisync AI Solutions | Melbourne Small Business Technology Solutions',
  description: 'Advisync AI Solutions provides affordable AI voice agents and automation solutions for Melbourne small businesses - specializing in tradies (electricians, plumbers, builders), healthcare providers (chiropractors, physios, dentists), and NDIS services.',
  keywords: [
    'AI voice agents Melbourne',
    'business automation Melbourne',
    'small business AI solutions',
    'tradies automation',
    'healthcare automation',
    'NDIS provider automation',
    'Melbourne AI solutions',
    'voice automation Australia'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/about'
  },
  openGraph: {
    title: 'About Advisync AI Solutions | Melbourne Small Business Technology Solutions',
    description: 'Affordable AI voice agents and automation solutions for Melbourne small businesses - specializing in tradies (electricians, plumbers, builders), healthcare providers (chiropractors, physios, dentists), and NDIS services.',
    url: 'https://advisync.com.au/about',
    type: 'website',
    siteName: 'Advisync AI Solutions',
    locale: 'en_AU',
    images: [
      {
        url: '/images/og/about-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI Solutions Technology Services'
      }
    ]
  }
};

interface ServiceOffering {
  id: string;
  title: string;
  description: string;
  icon: React.ElementType;
}

const serviceOfferings: ServiceOffering[] = [
  {
    id: 'ai-voice-agents',
    title: 'AI Voice Agents',
    description: 'Perfect for tradies, healthcare providers, and NDIS services. 24/7 AI receptionists that never miss a call, book appointments, and handle customer inquiries with authentic Australian voices.',
    icon: Bot
  },
  {
    id: 'business-automation',
    title: 'AI Workflow Automation',
    description: 'Ideal for busy tradies, healthcare practices, and NDIS providers. Simple automation solutions that connect your business tools and eliminate repetitive paperwork.',
    icon: Settings
  },
  {
    id: 'customer-management',
    title: 'Customer Management Automation',
    description: 'Perfect for service businesses that want to capture every lead and keep customers happy. Automated follow-ups, appointment management, and customer communication that works alongside your AI voice agents.',
    icon: Heart
  },
  {
    id: 'tech-consulting',
    title: 'Technology Consulting',
    description: 'Straightforward advice on the right technology investments for your specific business needs.',
    icon: Briefcase
  }
];

const businessValues = [
  {
    id: 'practicality',
    title: 'Practical Solutions',
    description: 'We focus on realistic, affordable technology that delivers immediate value to your business.',
    icon: Target,
    gradient: 'from-primary/40 to-secondary/40'
  },
  {
    id: 'transparency',
    title: 'Complete Transparency',
    description: 'Clear communication, honest timelines, and straightforward pricing with no hidden costs.',
    icon: Award,
    gradient: 'from-secondary/40 to-primary/40'
  },
  {
    id: 'education',
    title: 'Knowledge Sharing',
    description: "We believe in empowering clients to understand and eventually manage their own technology.",
    icon: Compass,
    gradient: 'from-primary/40 to-secondary/40'
  }
];

const businessAdvantages = [
  {
    id: 'affordable',
    title: 'Affordable Solutions',
    description: 'Technology services priced specifically for small business budgets',
    icon: CheckCircle
  },
  {
    id: 'tailored',
    title: 'Tailored Approach',
    description: 'Solutions designed specifically for your business needs and goals',
    icon: Target
  },
  {
    id: 'reliable',
    title: 'Reliable Support',
    description: 'Ongoing assistance and maintenance to keep your technology running smoothly',
    icon: Shield
  },
  {
    id: 'scalable',
    title: 'Scalable Technology',
    description: 'Solutions that grow with your business without requiring complete rebuilds',
    icon: Layers
  }
];

// Add JSON-LD structured data
const getJsonLd = () => ({
  '@context': 'https://schema.org',
  '@type': 'AboutPage',
  mainEntity: {
    '@type': 'Organization',
    name: 'Advisync AI Solutions',
    description: 'Melbourne-based technology solutions provider helping small businesses implement practical digital solutions.',
    url: 'https://advisync.com.au',
    location: {
      '@type': 'Place',
      address: {
        '@type': 'PostalAddress',
        addressLocality: 'Melbourne',
        addressRegion: 'VIC',
        addressCountry: 'AU'
      }
    },
    knowsAbout: [
      'AI Voice Agents',
      'Business Automation',
      'AI Implementation',
      'Small Business Technology',
      'NDIS Provider Solutions'
    ]
  }
});

export default function AboutPage() {
  return (
    <main className="py-10 md:py-20 overflow-hidden">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(getJsonLd()) }}
      />
      <Container>
        {/* Hero Section - Royal Sapphire & Gold Design */}
        <section className="text-center mb-16 md:mb-32 bg-bg-primary py-12 md:py-20 px-4 rounded-2xl">
          <div className="max-w-6xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold mb-4 md:mb-6 text-text-heading">
              Technology Solutions for
              <span className="text-accent-secondary"> Small Businesses</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-text-body max-w-3xl mx-auto mb-6 md:mb-12 leading-relaxed">
              Advisync AI Solutions provides affordable, practical digital solutions for Melbourne small businesses without the enterprise price tag.
            </p>
            <div className="flex flex-wrap justify-center gap-3 md:gap-4">
              <div className="flex items-center gap-2 px-3 md:px-4 py-1.5 md:py-2 bg-accent-primary/10 border border-accent-primary/20 rounded-full">
                <span className="w-2 h-2 bg-accent-primary rounded-full animate-pulse" />
                <span className="text-xs md:text-sm text-accent-primary font-medium">Small Business Focused</span>
              </div>
              <div className="flex items-center gap-2 px-3 md:px-4 py-1.5 md:py-2 bg-accent-secondary/10 border border-accent-secondary/20 rounded-full">
                <span className="w-2 h-2 bg-accent-secondary rounded-full animate-pulse" />
                <span className="text-xs md:text-sm text-accent-secondary font-medium">Practical Solutions</span>
              </div>
            </div>
          </div>
        </section>

        {/* About Section - Royal Sapphire & Gold Design */}
        <section className="mb-16 md:mb-32">
          <div className="bg-surface-card rounded-2xl p-6 md:p-12 border border-border-hair">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 md:mb-8 text-text-heading">
              About <span className="text-accent-secondary">Advisync Solutions</span>
            </h2>
            <div className="space-y-4 md:space-y-6">
              <p className="text-base md:text-lg text-text-body leading-relaxed">
                Advisync Solutions is a Melbourne-based technology solutions provider focused exclusively on helping small businesses access the same technology advantages as larger companies. We specialize in serving <span className="text-accent-secondary font-medium">tradies</span> (electricians, plumbers, builders), <span className="text-accent-secondary font-medium">healthcare providers</span> (chiropractors, physios, dentists), and <span className="text-accent-secondary font-medium">NDIS services</span> with practical, affordable solutions tailored to their specific needs.
              </p>
              <p className="text-base md:text-lg text-text-body leading-relaxed">
                We understand that small businesses face unique challenges when it comes to technology adoption. That's why we've developed a service approach that eliminates unnecessary complexity and focuses on delivering immediate value.
              </p>
              <p className="text-base md:text-lg text-text-body leading-relaxed">
                Our expertise spans AI voice agents, business automation, and strategic technology consulting—all delivered with a practical approach that prioritizes your business goals and budget constraints.
              </p>
            </div>
          </div>
        </section>

        {/* Mission & Approach - Royal Sapphire & Gold Design */}
        <section className="mb-16 md:mb-32">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-12">
            <div className="group bg-surface-card rounded-2xl p-6 md:p-12 border border-border-hair hover:border-accent-primary/30 transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-accent-primary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
              <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-6 text-text-heading">
                Our <span className="text-accent-secondary">Mission</span>
              </h2>
              <p className="text-base md:text-lg text-text-body leading-relaxed">
                To help small businesses in Melbourne access the same technology advantages as larger companies,
                but with solutions that are practical, affordable, and tailored to their specific needs.
              </p>
            </div>
            <div className="group bg-surface-card rounded-2xl p-6 md:p-12 border border-border-hair hover:border-accent-secondary/30 transition-all duration-300 relative overflow-hidden">
              <div className="absolute top-0 left-0 w-full h-1 bg-accent-secondary transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left" />
              <h2 className="text-2xl md:text-3xl font-bold mb-3 md:mb-6 text-text-heading">
                Our <span className="text-accent-secondary">Approach</span>
              </h2>
              <p className="text-base md:text-lg text-text-body leading-relaxed">
                We believe in starting small, focusing on immediate value, and growing technology solutions
                alongside your business. No unnecessary complexity or overengineering – just what works.
              </p>
            </div>
          </div>
        </section>

        {/* Services Section - Enhanced with better visual design */}
        <section className="mb-16 md:mb-32 relative">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-primary/5 to-transparent rounded-3xl -z-10 opacity-30" />
          <h2 className="text-2xl md:text-4xl font-bold text-center mb-8 md:mb-16 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary drop-shadow-sm">
            Our Services
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6 md:gap-8">
            {serviceOfferings.map((service, index) => (
              <article
                key={service.id}
                className="group bg-secondary/5 backdrop-blur-sm rounded-3xl p-6 md:p-8 border border-primary/10 hover:border-primary/20 transition-all duration-500 hover:translate-y-[-8px] relative overflow-hidden shadow-lg hover:shadow-xl"
                style={{ transitionDelay: `${index * 50}ms` }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 to-secondary/5 opacity-0 group-hover:opacity-20 transition-opacity duration-500" />
                <div className="mb-4 md:mb-6 relative">
                  <div className="p-3 bg-primary/10 rounded-2xl inline-block">
                    <service.icon className="w-6 h-6 md:w-7 md:h-7 text-primary" />
                  </div>
                </div>
                <h3 className="text-xl md:text-2xl font-bold mb-3 md:mb-4 group-hover:text-primary transition-colors duration-300">{service.title}</h3>
                <p className="text-sm md:text-base text-foreground/70 leading-relaxed">{service.description}</p>
              </article>
            ))}
          </div>
        </section>

        {/* Business Advantages - Enhanced with better visual elements */}
        <section className="mb-16 md:mb-32 relative">
          <div className="absolute inset-0 bg-gradient-to-tr from-secondary/5 to-transparent rounded-3xl -z-10 opacity-30" />
          <h2 className="text-2xl md:text-4xl font-bold text-center mb-8 md:mb-16 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary drop-shadow-sm">
            The Advisync Solutions Advantage
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-8 md:gap-12">
            {businessAdvantages.map((advantage, index) => (
              <div
                key={advantage.id}
                className="flex gap-5 group p-5 rounded-2xl hover:bg-secondary/5 transition-colors duration-300"
                style={{ transitionDelay: `${index * 50}ms` }}
              >
                <div className="flex-shrink-0 mt-1">
                  <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                    <advantage.icon className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300">{advantage.title}</h3>
                  <p className="text-foreground/70 leading-relaxed">
                    {advantage.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Small Business Focus - Enhanced with better visual design */}
        <section className="mb-16 md:mb-32 relative">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-transparent to-secondary/5 rounded-3xl -z-10 opacity-30" />
          <div className="bg-secondary/5 backdrop-blur-sm rounded-3xl p-6 md:p-12 border border-primary/10 shadow-lg relative overflow-hidden">
            <div className="absolute top-0 right-0 w-40 h-40 bg-gradient-to-b from-primary/10 to-secondary/10 rounded-full blur-3xl -z-10 opacity-60" />
            <div className="absolute bottom-0 left-0 w-40 h-40 bg-gradient-to-t from-primary/10 to-secondary/10 rounded-full blur-3xl -z-10 opacity-60" />
            <h2 className="text-2xl md:text-3xl font-bold mb-6 md:mb-8 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
              Why Small Businesses Choose Advisync AI Solutions
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12">
              <div className="flex gap-5 group">
                <div className="flex-shrink-0 mt-1">
                  <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                    <Users className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300">Personal Attention</h3>
                  <p className="text-foreground/70 leading-relaxed">
                    We understand small business challenges and provide the personal attention that larger agencies can't match.
                  </p>
                </div>
              </div>
              <div className="flex gap-5 group">
                <div className="flex-shrink-0 mt-1">
                  <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                    <Clock className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300">Quick Implementation</h3>
                  <p className="text-foreground/70 leading-relaxed">
                    We focus on solutions that can be implemented quickly and start delivering value to your business right away.
                  </p>
                </div>
              </div>
              <div className="flex gap-5 group">
                <div className="flex-shrink-0 mt-1">
                  <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                    <Zap className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300">Practical Technology</h3>
                  <p className="text-foreground/70 leading-relaxed">
                    No buzzwords or unnecessary complexity – just straightforward technology solutions that solve real business problems.
                  </p>
                </div>
              </div>
              <div className="flex gap-5 group">
                <div className="flex-shrink-0 mt-1">
                  <div className="p-3 bg-primary/10 rounded-xl group-hover:bg-primary/20 transition-colors duration-300">
                    <Heart className="w-6 h-6 text-primary" />
                  </div>
                </div>
                <div>
                  <h3 className="text-lg md:text-xl font-semibold mb-2 group-hover:text-primary transition-colors duration-300">Genuine Care</h3>
                  <p className="text-foreground/70 leading-relaxed">
                    Your success is our success. We're building our business by helping small businesses thrive.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Values Section - Enhanced with better visual elements */}
        <section className="mb-16 md:mb-32 relative">
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-secondary/5 to-transparent rounded-3xl -z-10 opacity-30" />
          <h2 className="text-2xl md:text-4xl font-bold text-center mb-8 md:mb-16 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary drop-shadow-sm">
            Our Values
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 md:gap-8">
            {businessValues.map((value, index) => (
              <div
                key={value.id}
                className="group bg-secondary/5 backdrop-blur-sm rounded-3xl p-6 md:p-10 border border-primary/10 hover:border-primary/20 transition-all duration-500 hover:translate-y-[-8px] relative overflow-hidden shadow-lg hover:shadow-xl"
                style={{ transitionDelay: `${index * 100}ms` }}
              >
                <div className={`absolute inset-0 bg-gradient-to-br ${value.gradient} opacity-0 group-hover:opacity-20 transition-opacity duration-500`} />
                <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-primary/40 to-secondary/40 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left" />
                <div className="relative">
                  <div className="mb-4 md:mb-6">
                    <div className="p-3 bg-primary/10 rounded-2xl inline-block group-hover:bg-primary/20 transition-colors duration-300">
                      <value.icon className="w-6 h-6 md:w-7 md:h-7 text-primary" />
                    </div>
                  </div>
                  <h3 className="text-xl md:text-2xl font-bold mb-3 md:mb-4 group-hover:text-primary transition-colors duration-300">{value.title}</h3>
                  <p className="text-sm md:text-base text-foreground/70 leading-relaxed">{value.description}</p>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* CTA Section - Enhanced with better visual elements */}
        <section className="relative mx-4 md:mx-0 mt-8 md:mt-16">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 rounded-3xl -z-10" />
          <div className="absolute inset-0 overflow-hidden rounded-3xl">
            <div className="absolute -top-40 -right-40 w-80 h-80 bg-primary/10 rounded-full blur-3xl" />
            <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-secondary/10 rounded-full blur-3xl" />
          </div>
          <div className="text-center p-8 md:p-16 relative z-10">
            <h2 className="text-2xl md:text-4xl font-bold mb-4 md:mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary leading-tight drop-shadow-sm">
              Let's Talk About Your Business
            </h2>
            <p className="text-base md:text-xl text-foreground/80 mb-8 md:mb-12 max-w-2xl mx-auto leading-relaxed">
              We offer a free, no-obligation consultation to understand your business needs and explore how Advisync Solutions can help you grow.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-5 max-w-md sm:max-w-none mx-auto">
              <Link
                href="/consultation"
                className="w-full sm:w-auto group inline-flex items-center justify-center px-6 md:px-8 py-3.5 md:py-4 rounded-lg bg-primary text-white hover:bg-primary/90 transition-all duration-300 shadow-lg hover:shadow-xl hover:translate-y-[-2px]"
              >
                <span className="font-medium whitespace-nowrap">Book a Free Consultation</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
              <Link
                href="/services"
                className="w-full sm:w-auto group inline-flex items-center justify-center px-6 md:px-8 py-3.5 md:py-4 rounded-lg bg-secondary/10 text-secondary hover:bg-secondary/20 transition-all duration-300 border border-secondary/20 shadow-lg hover:shadow-xl hover:translate-y-[-2px]"
              >
                <span className="font-medium whitespace-nowrap">Explore Services</span>
                <ArrowRight className="w-4 h-4 md:w-5 md:h-5 ml-2 transform transition-transform duration-300 group-hover:translate-x-1" />
              </Link>
            </div>
            <p className="mt-5 md:mt-8 text-xs md:text-sm text-foreground/50 italic">
              No obligations, no pressure - just practical advice for your business
            </p>
          </div>
        </section>
      </Container>
    </main>
  );
}