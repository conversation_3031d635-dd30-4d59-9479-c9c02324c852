import { Metadata } from 'next';
import { Suspense, lazy } from 'react';
import { HeroSection } from "@/components/features/hero-section";
import { SavingsCalculator } from "@/components/features/savings-calculator";
import { ServicesOverview } from "@/components/features/services-overview";
import { WhyChooseUs } from "@/components/sections/why-choose-us";
import { AutomationShowcase } from "@/components/sections/automation-showcase";
import { AISolutions } from "@/components/features/ai-solutions";
import { PartnerLogos } from "@/components/sections/partner-logos";
import { LocalBusinessSchema } from "@/components/seo/local-business-schema";
// Import components that will be lazy loaded
import { LazyLoadSection } from '@/components/common/lazy-load-section';
import Loading from '@/components/common/loading';

// Use lazy loading for components below the fold
const PricingSection = lazy(() => import("@/components/sections/pricing").then(mod => ({ default: mod.PricingSection })));
const CaseStudies = lazy(() => import("@/components/sections/case-studies").then(mod => ({ default: mod.CaseStudies })));
const FAQ = lazy(() => import("@/components/sections/faq").then(mod => ({ default: mod.FAQ })));
const ConsultationCTA = lazy(() => import("@/components/sections/consultation-cta").then(mod => ({ default: mod.ConsultationCTA })));

export const metadata: Metadata = {
  title: "AI Voice Agents & Workflow Automation | Advisync AI",
  description: "Australian AI voice agents providing 24/7 customer service and workflow automation solutions. Reduce call wait times and streamline operations with our virtual receptionist technology.",
  keywords: [
    'ai voice agent australia',
    'website voice bot demo',
    'virtual receptionist melbourne',
    'n8n automation consultant',
    'business process automation melbourne',
    '24/7 customer service solution',
    'hands-free lead follow-up',
    'reduce call wait times',
    'automate data entry for smb',
    'ai workflow builder',
    'melbourne ai agency',
    'voice assistant integration',
    'australian virtual receptionist',
    'business automation melbourne',
    'ai customer service australia'
  ],
  alternates: {
    canonical: 'https://advisync.com.au'
  },
  openGraph: {
    title: "AI Voice Agents & Workflow Automation | Advisync AI",
    description: "Australian AI voice agents providing 24/7 customer service and workflow automation solutions. Virtual receptionist technology for Melbourne businesses.",
    url: 'https://advisync.com.au',
    siteName: 'Advisync AI',
    locale: 'en_AU',
    type: 'website',
    images: [
      {
        url: '/images/og/home-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync AI - Australian AI Voice Agents & Workflow Automation'
      }
    ]
  },
  twitter: {
    card: 'summary_large_image',
    title: "AI Voice Agents & Workflow Automation | Advisync AI",
    description: "Australian AI voice agents providing 24/7 customer service and workflow automation solutions.",
    images: ['/images/og/home-og.jpg']
  }
};

// Add JSON-LD structured data
const jsonLd = {
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Advisync AI',
  description: 'Australian AI-automation agency specializing in voice agents, virtual receptionists, and workflow automation for businesses.',
  url: 'https://advisync.com.au',
  logo: 'https://advisync.com.au/images/advisync-logo.png',
  address: {
    '@type': 'PostalAddress',
    addressLocality: 'Melbourne',
    addressRegion: 'VIC',
    postalCode: '3000',
    addressCountry: 'AU'
  },
  contactPoint: {
    '@type': 'ContactPoint',
    telephone: '+61-***********',
    contactType: 'customer service',
    email: '<EMAIL>'
  },
  sameAs: [
    'https://www.linkedin.com/company/advisync-solutions',
    'https://x.com/Advisync_AI_Sol',
    'https://www.facebook.com/profile.php?id=61573127892569',
    'https://www.instagram.com/advisync_solutions_/'
  ],
  areaServed: {
    '@type': 'Country',
    name: 'Australia'
  },
  taxID: '12 ***********'
};

export default function Home() {
  return (
    <main>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
      />
      <LocalBusinessSchema />
      {/* Above the fold content (loads immediately) */}
      <HeroSection />
      <SavingsCalculator />
      <PartnerLogos />
      <ServicesOverview />
      <WhyChooseUs />

      {/* Consolidate automation and AI sections for more impact */}
      <AutomationShowcase />
      <AISolutions />

      {/* Pricing Section */}
      <LazyLoadSection>
        <Suspense fallback={<Loading />}>
          <PricingSection />
        </Suspense>
      </LazyLoadSection>

      {/* Below the fold content (lazy loaded) */}
      <LazyLoadSection>
        <Suspense fallback={<Loading />}>
          <CaseStudies />
        </Suspense>
      </LazyLoadSection>

      <LazyLoadSection>
        <Suspense fallback={<Loading />}>
          <FAQ />
        </Suspense>
      </LazyLoadSection>

      <LazyLoadSection>
        <Suspense fallback={<Loading />}>
          <ConsultationCTA />
        </Suspense>
      </LazyLoadSection>
    </main>
  );
}