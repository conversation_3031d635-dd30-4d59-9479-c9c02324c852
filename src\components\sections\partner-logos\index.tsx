'use client';

import { motion } from 'framer-motion';
import { Container } from '@/components/common/container';
import { OptimizedImage } from '@/components/common/optimized-image';
import { cn } from '@/lib/utils';

interface PartnerLogo {
  name: string;
  logo: string;
  url: string;
  alt: string;
}

const partners: PartnerLogo[] = [
  {
    name: 'ElevenLabs',
    logo: '/images/partners/elevenlabs-logo.svg',
    url: 'https://elevenlabs.io/',
    alt: 'ElevenLabs - AI Voice Technology Partner'
  },
  {
    name: 'Vapi',
    logo: '/images/partners/vapi-logo.svg',
    url: 'https://vapi.ai/',
    alt: 'Vapi - Voice AI Platform Partner'
  },
  {
    name: 'Retell',
    logo: '/images/partners/retell-logo.svg',
    url: 'https://retellai.com/',
    alt: 'Retell - AI Voice Agent Partner'
  },
  {
    name: 'Make.com',
    logo: '/images/partners/make-logo.svg',
    url: 'https://make.com/',
    alt: 'Make.com - Automation Platform Partner'
  },
  {
    name: 'N8N',
    logo: '/images/partners/n8n-logo.svg',
    url: 'https://n8n.io/',
    alt: 'N8N - Workflow Automation Platform'
  }
];

interface PartnerLogosProps {
  className?: string;
}

export function PartnerLogos({ className }: PartnerLogosProps) {
  return (
    <section className={cn("py-6 md:py-8 bg-secondary/5 border-y border-primary/10", className)}>
      <Container>
        <div className="flex flex-col items-center">
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="text-sm text-foreground/60 mb-4 md:mb-6 text-center"
          >
            Powered by industry-leading platforms
          </motion.p>

          <motion.div
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex flex-wrap justify-center items-center gap-6 md:gap-8 lg:gap-12"
          >
            {partners.map((partner, index) => (
              <motion.a
                key={partner.name}
                href={partner.url}
                target="_blank"
                rel="noopener noreferrer"
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: 0.1 * index }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="relative group"
                aria-label={partner.name}
              >
                <div className="absolute -inset-2 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"></div>
                <OptimizedImage
                  src={partner.logo}
                  alt={partner.alt}
                  width={120}
                  height={40}
                  className="h-[30px] sm:h-[35px] md:h-[40px] w-auto object-contain opacity-60 group-hover:opacity-100 transition-all duration-300 relative filter grayscale group-hover:grayscale-0"
                  sizes="(max-width: 768px) 80px, 120px"
                  quality={90}
                />
              </motion.a>
            ))}
          </motion.div>
        </div>
      </Container>
    </section>
  );
}
