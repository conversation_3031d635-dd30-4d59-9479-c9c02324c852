'use client';

import { motion } from 'framer-motion';
import Link from 'next/link';
import { ArrowRight, Clock, Tag } from 'lucide-react';
import { Container } from '@/components/common/container';
import { BlogImage } from '@/components/common/blog-image';
import { blogPosts } from '@/config/blog-posts';

const fadeInUp = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.5 }
};

export function BlogSection() {
  // Get featured and recent posts
  const featuredPost = blogPosts.find(post => post.featured);
  const recentPosts = blogPosts
    .filter(post => !post.featured)
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 3);

  return (
    <section className="py-20">
      <Container>
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2 
            className="text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary"
            {...fadeInUp}
          >
            Melbourne Business Insights
          </motion.h2>
          <motion.p 
            className="text-xl text-foreground/60 max-w-2xl mx-auto"
            {...fadeInUp}
            transition={{ delay: 0.1 }}
          >
            Stay updated with the latest trends and strategies in digital transformation, automation, and technology for Melbourne small businesses.
          </motion.p>
        </div>

        {/* Featured Post */}
        {featuredPost && (
          <motion.div 
            className="mb-16"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
          >
            <Link href={`/blog/${featuredPost.id}`} className="group">
              <article className="relative bg-secondary/5 backdrop-blur-sm rounded-3xl p-8 border border-primary/10 hover:border-primary/20 transition-all duration-300">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
                <div className="grid md:grid-cols-2 gap-8 items-center">
                  <div className="relative aspect-video rounded-2xl overflow-hidden">
                    <BlogImage
                      src={featuredPost.image}
                      alt={featuredPost.title}
                      priority={true}
                      size="large"
                      width={800}
                      height={450}
                      className="group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                  <div>
                    <span className="text-sm font-medium text-primary/80 mb-2 block">
                      Featured Post
                    </span>
                    <h3 className="text-2xl md:text-3xl font-bold mb-4 group-hover:text-primary transition-colors">
                      {featuredPost.title}
                    </h3>
                    <p className="text-foreground/60 mb-6">
                      {featuredPost.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-foreground/40 mb-6">
                      <span className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        {featuredPost.readTime}
                      </span>
                      <span className="flex items-center gap-1">
                        <Tag className="w-4 h-4" />
                        {featuredPost.category}
                      </span>
                    </div>
                    <div className="group/btn inline-flex items-center gap-2 text-primary">
                      <span>Read Article</span>
                      <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
                    </div>
                  </div>
                </div>
              </article>
            </Link>
          </motion.div>
        )}

        {/* Recent Posts Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {recentPosts.map((post, index) => (
            <motion.div
              key={post.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <Link 
                href={`/blog/${post.id}`}
                className="group block relative bg-secondary/5 backdrop-blur-sm rounded-2xl overflow-hidden border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:translate-y-[-4px]"
              >
                <div className="relative aspect-video">
                  <BlogImage
                    src={post.image}
                    alt={post.title}
                    size="medium"
                    width={600}
                    height={338}
                    className="group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="p-6">
                  <div className="flex items-center gap-4 text-sm text-foreground/40 mb-4">
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {post.readTime}
                    </span>
                    <span className="flex items-center gap-1">
                      <Tag className="w-4 h-4" />
                      {post.category}
                    </span>
                  </div>
                  <h3 className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary transition-colors">
                    {post.title}
                  </h3>
                  <p className="text-foreground/60 mb-6 line-clamp-3">
                    {post.description}
                  </p>
                  <div className="flex items-center gap-2 text-primary group-hover:text-primary/80 transition-colors">
                    <span>Read More</span>
                    <ArrowRight className="w-4 h-4 transform transition-transform group-hover:translate-x-1" />
                  </div>
                </div>
              </Link>
            </motion.div>
          ))}
        </div>

        {/* View All Button */}
        <motion.div 
          className="text-center mt-12"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <Link 
            href="/blog" 
            className="inline-flex items-center gap-2 text-lg font-medium text-primary hover:text-primary/80 transition-colors"
          >
            <span>View All Posts</span>
            <ArrowRight className="w-5 h-5 transition-transform group-hover:translate-x-1" />
          </Link>
        </motion.div>
      </Container>
    </section>
  );
} 