'use client';

import Link from 'next/link';
import { motion, useInView } from 'framer-motion';
import {
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
  Linkedin,
  ArrowRight,
  Globe,
  Clock,
  Shield,
  BookOpen,
  AlertCircle,
  CheckCircle,
  ChevronRight,
  ExternalLink,
  Sparkles,
  Loader2,
  Calendar,
  Users,
  Bot,
  Code2,
  Zap,
  MessageSquare,
  FileText
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { Logo } from '@/components/common/logo';
import { useState, useRef } from 'react';
import { contactInfo, socialMedia } from '@/config/contact';

const footerLinks = [
  {
    title: "Solutions",
    icon: Zap,
    links: [
      { label: "AI Voice Agents", href: "/services/ai-voice-agents" },
      { label: "Business Automation", href: "/services/business-automation" },
      { label: "Customer Management", href: "/services/customer-management" }
    ]
  },
  {
    title: "Company",
    icon: Users,
    links: [
      { label: "About Us", href: "/about" },
      { label: "Case Studies", href: "/case-studies" },
      { label: "Blog", href: "/blog" }
    ]
  },
  {
    title: "Legal",
    icon: Shield,
    links: [
      { label: "Privacy Policy", href: "/privacy" },
      { label: "Terms of Service", href: "/terms" },
      { label: "Contact", href: "/contact" }
    ]
  }
];

const companyFeatures = [
  {
    icon: Globe,
    text: "Melbourne-based digital solutions",
    color: "text-accent-primary"
  },
  {
    icon: Clock,
    text: contactInfo.hours + " support available",
    color: "text-accent-secondary"
  },
  {
    icon: Shield,
    text: "Australian owned & operated",
    color: "text-accent-primary"
  },
  {
    icon: Bot,
    text: "AI-powered solutions",
    color: "text-accent-secondary"
  }
];

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.1,
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.5, ease: "easeOut" }
  }
};

export function Footer() {
  const [email, setEmail] = useState('');
  const [status, setStatus] = useState<'idle' | 'loading' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');

  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.log("Footer form submitted with email:", email); // Debug log

    if (!email || !email.includes('@')) {
      setStatus('error');
      setMessage('Please enter a valid email address');
      return;
    }

    try {
      setStatus('loading');
      console.log("Sending request to /api/subscribe"); // Debug log

      const response = await fetch('/api/subscribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      console.log("Response received:", response.status); // Debug log
      const data = await response.json();
      console.log("Response data:", data); // Debug log

      if (response.ok) {
        setStatus('success');
        setMessage(data.message);
        setEmail('');
      } else {
        setStatus('error');
        setMessage(data.message || 'Something went wrong. Please try again.');
      }
    } catch (error) {
      console.error('Newsletter subscription error:', error);
      setStatus('error');
      setMessage('Something went wrong. Please try again.');
    }
  };

  return (
    <footer ref={ref} className="bg-background relative overflow-hidden border-t border-primary/10">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background pointer-events-none" />

      <Container className="relative z-10">
        {/* Main Footer Content */}
        <motion.div
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          variants={containerVariants}
          className="py-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-12 gap-12 relative"
        >
          {/* Logo and Contact Section */}
          <motion.div variants={itemVariants} className="lg:col-span-4 space-y-6">
            <div className="space-y-4">
              <div className="relative group">
                <div className="absolute -inset-4 bg-gradient-to-r from-primary/20 to-secondary/20 blur-xl rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
                <Logo width={160} height={53} className="relative mx-auto md:mx-0" />
              </div>
              <p className="text-foreground/70 max-w-sm text-sm sm:text-base text-center md:text-left">
                Empowering Melbourne businesses with cutting-edge digital solutions and intelligent automation expertise.
              </p>
            </div>

            {/* Contact Information */}
            <div className="flex flex-wrap gap-4 justify-center md:justify-start">
              <a
                href={`mailto:${contactInfo.email}`}
                className="text-sm text-foreground/60 hover:text-primary transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Mail size={16} />
                  {contactInfo.email}
                </span>
              </a>
              <a
                href={`tel:${contactInfo.phone}`}
                className="text-sm text-foreground/60 hover:text-primary transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Phone size={16} />
                  {contactInfo.phone}
                </span>
              </a>
              <a
                href={`https://maps.google.com/?q=${contactInfo.address.locality},${contactInfo.address.region}`}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-foreground/60 hover:text-primary transition-colors"
              >
                <span className="flex items-center gap-2">
                  <MapPin size={16} />
                  {`${contactInfo.address.locality}, ${contactInfo.address.region} ${contactInfo.address.postalCode}`}
                </span>
              </a>
              <div className="text-sm text-foreground/60">
                <span className="flex items-center gap-2">
                  <FileText size={16} />
                  ABN: {contactInfo.abn}
                </span>
              </div>
              <a
                href={contactInfo.googleBusinessProfile}
                target="_blank"
                rel="noopener noreferrer"
                className="text-sm text-foreground/60 hover:text-primary transition-colors"
              >
                <span className="flex items-center gap-2">
                  <Globe size={16} />
                  Google Business Profile
                </span>
              </a>
            </div>
          </motion.div>

          {/* Links Sections */}
          <motion.div variants={itemVariants} className="lg:col-span-8 grid grid-cols-3 gap-8">
            {footerLinks.map((section) => (
              <div key={section.title} className="space-y-4">
                <div className="flex items-center gap-2">
                  <section.icon size={14} className="text-primary/70" />
                  <h3 className="text-base font-semibold text-foreground/90">
                    {section.title}
                  </h3>
                </div>
                <ul className="space-y-2">
                  {section.links.map((link) => (
                    <motion.li
                      key={link.label}
                      whileHover={{ x: 5 }}
                    >
                      <Link
                        href={link.href}
                        className="text-sm text-foreground/60 hover:text-primary transition-colors"
                      >
                        {link.label}
                      </Link>
                    </motion.li>
                  ))}
                </ul>
              </div>
            ))}
          </motion.div>
        </motion.div>

        {/* Newsletter Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="relative py-12 sm:py-16"
        >
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-2xl"></div>
          <div className="absolute inset-0 bg-black/50 backdrop-blur-sm rounded-2xl"></div>

          <div className="relative z-10 px-6 sm:px-10 md:px-16">
            {status === 'success' ? (
              <div className="flex flex-col items-center justify-center py-8">
                <div className="w-16 h-16 bg-green-100/10 rounded-full flex items-center justify-center mb-6">
                  <CheckCircle className="w-8 h-8 text-green-400" />
                </div>
                <h2 className="text-2xl md:text-3xl font-bold mb-4 text-white">
                  Subscription Successful!
                </h2>
                <p className="text-lg text-white/70 mb-4 max-w-2xl mx-auto text-center">
                  {message}
                </p>
                <p className="text-sm text-white/60 max-w-2xl mx-auto text-center">
                  Please check your inbox for a confirmation email. If you don't see it, please check your spam folder.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <motion.div variants={itemVariants} className="text-center lg:text-left">
                  <h3 className="text-xl sm:text-2xl font-bold mb-3 inline-flex items-center gap-2">
                    <div className="w-8 h-8 rounded-full bg-accent-secondary/20 flex items-center justify-center">
                      <Sparkles size={16} className="text-accent-secondary" />
                    </div>
                    <span className="bg-clip-text text-transparent bg-gradient-to-r from-white to-accent-secondary/80">
                      Stay at the Cutting Edge
                    </span>
                  </h3>
                  <p className="text-sm sm:text-base text-white/70 max-w-md">
                    Subscribe to receive the latest insights on business automation, AI solutions,
                    and digital transformation strategies for Melbourne businesses.
                  </p>
                </motion.div>

                <motion.form variants={itemVariants} onSubmit={handleSubmit} className="flex flex-col gap-3">
                  <div className="flex flex-col sm:flex-row gap-3">
                    <div className="relative flex-1 group">
                      <input
                        type="email"
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        disabled={status === 'loading'}
                        className="w-full px-5 py-3 pl-12 bg-white/5 border border-white/10 hover:border-primary/30 focus:border-primary/50 rounded-xl focus:outline-none focus:ring-1 focus:ring-primary/30 transition-all text-sm text-white backdrop-blur-sm group-hover:bg-white/10 z-10"
                      />
                      <Mail
                        size={18}
                        className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white/40 group-hover:text-white/60 transition-colors"
                      />
                    </div>
                    <button
                      type="submit"
                      disabled={status === 'loading'}
                      className="relative px-5 py-3 bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-white rounded-xl transition-all flex items-center justify-center gap-2 text-sm font-medium group touch-manipulation shadow-lg shadow-primary/20 hover:shadow-xl hover:shadow-primary/30 cursor-pointer z-10"
                      onClick={(e) => {
                        console.log("Footer button clicked"); // Debug log
                        if (status !== 'loading') {
                          e.currentTarget.form?.dispatchEvent(new Event('submit', { cancelable: true, bubbles: true }));
                        }
                      }}
                    >
                      {status === 'loading' ? (
                        <>
                          <Loader2 className="w-4 h-4 animate-spin" />
                          <span>Subscribing...</span>
                        </>
                      ) : (
                        <>
                          <span>Subscribe</span>
                          <ArrowRight size={16} className="transform group-hover:translate-x-1 transition-transform" />
                        </>
                      )}
                    </button>
                  </div>

                  {/* Subscription Status */}
                  {message && status === 'error' && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="flex items-center gap-2 text-red-400 text-sm mt-1 bg-red-500/10 px-4 py-2 rounded-lg border border-red-500/20"
                    >
                      <AlertCircle size={16} />
                      <span>{message}</span>
                    </motion.div>
                  )}
                </motion.form>
              </div>
            )}
          </div>
        </motion.div>

        {/* Social Links and Copyright */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="py-8 border-t border-primary/10 flex flex-col md:flex-row justify-between items-center gap-6"
        >
          <motion.div variants={itemVariants} className="flex gap-4">
            {Object.values(socialMedia).map((social) => (
              <a
                key={social.label}
                href={social.url}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={social.label}
                className="text-foreground/60 hover:text-primary transition-colors"
              >
                {social.label === 'Facebook' && <Facebook size={20} />}
                {social.label === 'X' && <Twitter size={20} />}
                {social.label === 'Instagram' && <Instagram size={20} />}
                {social.label === 'LinkedIn' && <Linkedin size={20} />}
              </a>
            ))}
          </motion.div>

          <motion.div
            variants={itemVariants}
            className="text-center md:text-right text-sm text-foreground/60"
          >
            <p>© {new Date().getFullYear()} Advisync Solutions. All rights reserved.</p>
          </motion.div>
        </motion.div>
      </Container>
    </footer>
  );
}