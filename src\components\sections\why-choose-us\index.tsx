'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, useInView } from 'framer-motion';
import {
  MapPin,
  LineChart,
  Users,
  Rocket,
  CheckCircle2,
  Clock,
  Shield,
  HeartHandshake,
  ArrowLeft,
  ArrowRight,
  Coffee,
  Wrench,
  Stethoscope,
  Scissors,
  Dumbbell,
  Heart,
  Croissant,
  ShoppingBag,
  Flower2,
  UtensilsCrossed,
  Bot
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { cn } from '@/lib/utils';

const features = [
  {
    title: "Australian-Based Support",
    description: "Local team providing personalized support and quick response times for Australian businesses.",
    icon: MapPin,
    stats: { value: 15, label: "Minute Response Time" }
  },
  {
    title: "Voice AI Expertise",
    description: "Specialized knowledge in voice agent technology and natural language processing.",
    icon: Bot,
    stats: { value: 98, label: "% Customer Satisfaction" }
  },
  {
    title: "Automation Results",
    description: "Measurable improvements in efficiency and cost savings through workflow automation.",
    icon: Line<PERSON><PERSON>,
    stats: { value: 70, label: "% Average Time Saved" }
  },
  {
    title: "Rapid Implementation",
    description: "Quick deployment of voice agents and automation solutions for immediate business value.",
    icon: Rocket,
    stats: { value: 14, label: "Day Implementation" }
  }
];

const benefits = [
  {
    icon: CheckCircle2,
    text: "Fixed pricing with no hidden costs or surprises"
  },
  {
    icon: Clock,
    text: "24/7 customer service with AI voice agents"
  },
  {
    icon: Shield,
    text: "Australian data hosting and privacy compliance"
  },
  {
    icon: HeartHandshake,
    text: "Ongoing support and training for your team"
  }
];

const testimonials = [
  {
    quote: "The scheduling system has been a game-changer for our small café. We've cut down appointment mix-ups by 90% and saved about 10 hours a week on staff rostering.",
    author: "Sarah Chen",
    role: "Owner",
    business: "Little Collins Café",
    location: "Carlton",
    icon: Coffee
  },
  {
    quote: "As a local plumber, keeping track of jobs was a nightmare. Their automation system has simplified my bookings and invoicing. I'm saving at least 5 hours a week on paperwork.",
    author: "James O'Connor",
    role: "Owner",
    business: "Reliable Plumbing Services",
    location: "Brunswick",
    icon: Wrench
  },
  {
    quote: "Our small dental practice was struggling with no-shows. Their automated reminder system has reduced cancellations by 60%. Worth every penny!",
    author: "Dr. Emily Wong",
    role: "Principal Dentist",
    business: "Smile Well Dental",
    location: "South Yarra",
    icon: Stethoscope
  },
  {
    quote: "The online booking system has transformed our hair salon. Clients love booking their own appointments, and we've seen a 40% reduction in phone calls.",
    author: "Maria Rossi",
    role: "Owner",
    business: "Style Hub",
    location: "Richmond",
    icon: Scissors
  },
  {
    quote: "Their automation tools helped our small gym handle membership renewals and class bookings. We've grown from 50 to 150 members without adding admin staff.",
    author: "Tom Mitchell",
    role: "Owner",
    business: "FitLife Studio",
    location: "Prahran",
    icon: Dumbbell
  },
  {
    quote: "As a solo physiotherapist, I was spending hours on admin. Now everything from bookings to follow-ups is automated, giving me more time with patients.",
    author: "Lisa Zhang",
    role: "Principal Physiotherapist",
    business: "Active Life Physio",
    location: "Hawthorn",
    icon: Heart
  },
  {
    quote: "The customer reminder system has been brilliant for our small bakery's order management. We've reduced order mix-ups by 80%.",
    author: "David Wilson",
    role: "Owner",
    business: "Daily Bread",
    location: "Northcote",
    icon: Croissant
  },
  {
    quote: "Their system streamlined our boutique's inventory and online orders. We've saved about 15 hours weekly on manual data entry.",
    author: "Emma Thompson",
    role: "Owner",
    business: "Style & Grace Boutique",
    location: "Brighton",
    icon: ShoppingBag
  },
  {
    quote: "The automated booking system has been perfect for our small yoga studio. Class bookings have increased by 35% since clients can book 24/7.",
    author: "Maya Patel",
    role: "Studio Owner",
    business: "Zen Yoga Studio",
    location: "Fitzroy",
    icon: Flower2
  },
  {
    quote: "Their automation tools helped our family restaurant manage online orders and table bookings. We've reduced booking errors to almost zero.",
    author: "John Dimitriou",
    role: "Owner",
    business: "Mediterranean Kitchen",
    location: "Malvern",
    icon: UtensilsCrossed
  }
];

function AnimatedCounter({ value, label }: { value: number, label: string }) {
  const counterRef = useRef(null);
  const isInView = useInView(counterRef, { once: true });
  const [count, setCount] = useState(0);

  useEffect(() => {
    if (isInView) {
      const duration = 2000; // 2 seconds
      const steps = 60;
      const increment = value / steps;
      const interval = duration / steps;

      let currentCount = 0;
      const timer = setInterval(() => {
        currentCount += increment;
        if (currentCount >= value) {
          setCount(value);
          clearInterval(timer);
        } else {
          setCount(Math.floor(currentCount));
        }
      }, interval);

      return () => clearInterval(timer);
    }
  }, [isInView, value]);

  return (
    <div ref={counterRef} className="text-center">
      <div className="text-3xl font-bold text-accent-secondary">
        {count}{value === 98 && '%'}+
      </div>
      <div className="text-sm text-foreground/60 mt-1">
        {label}
      </div>
    </div>
  );
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
  },
};

function TestimonialCarousel() {
  const [hovering, setHovering] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const scrollContainer = containerRef.current;
    if (!scrollContainer || hovering) return;

    let animationFrameId: number;
    let currentPosition = 0;

    const animate = () => {
      currentPosition -= 0.7;

      if (Math.abs(currentPosition) >= scrollContainer.scrollWidth / 2) {
        currentPosition = 0;
      }

      scrollContainer.style.transform = `translateX(${currentPosition}px)`;
      animationFrameId = requestAnimationFrame(animate);
    };

    animationFrameId = requestAnimationFrame(animate);

    return () => {
      cancelAnimationFrame(animationFrameId);
    };
  }, [hovering]);

  return (
    <div className="mt-16">
      <div className="text-center mb-12">
        <h3 className="text-3xl md:text-4xl font-bold mb-4 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary tracking-tight leading-tight">
          Australian Success Stories
        </h3>
        <p className="text-white/70 leading-relaxed">
          Real results from businesses using our AI voice agents and automation
        </p>
      </div>

      <div
        className="relative overflow-hidden py-8"
        onMouseEnter={() => setHovering(true)}
        onMouseLeave={() => setHovering(false)}
      >
        <div className="relative w-full">
          <div
            ref={containerRef}
            className="flex gap-8"
            style={{ willChange: 'transform' }}
          >
            {[...testimonials, ...testimonials].map((testimonial, index) => (
              <motion.div
                key={index}
                className="min-w-[400px] max-w-[400px] group"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <div className="relative bg-secondary/5 backdrop-blur-sm rounded-2xl p-6 border border-primary/10 overflow-hidden">
                  <div className="absolute top-4 right-4 text-primary/10">
                    <testimonial.icon className="w-24 h-24" />
                  </div>

                  <div className="relative">
                    <div className="inline-flex items-center gap-1 px-3 py-1 rounded-full bg-primary/10 text-primary text-sm mb-4">
                      <MapPin className="w-3 h-3" />
                      {testimonial.location}
                    </div>

                    <h4 className="text-xl font-semibold mb-2 text-foreground/90">
                      {testimonial.business}
                    </h4>

                    <blockquote className="text-base text-foreground/70 italic mb-6 min-h-[80px]">
                      "{testimonial.quote}"
                    </blockquote>

                    <div className="flex items-center gap-4 border-t border-primary/10 pt-4">
                      <div className="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 text-primary">
                        <testimonial.icon className="w-6 h-6" />
                      </div>
                      <div>
                        <cite className="text-accent-secondary font-semibold not-italic block">
                          {testimonial.author}
                        </cite>
                        <span className="text-sm text-foreground/60">
                          {testimonial.role}
                        </span>
                      </div>
                    </div>

                    <div className="absolute top-4 right-4 flex items-center gap-1 text-sm font-medium text-accent-secondary">
                      <LineChart className="w-4 h-4" />
                      <span>
                        {testimonial.quote.match(/\d+%/)?.[0] || "↑"}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        <div className="absolute left-0 top-0 bottom-0 w-40 bg-gradient-to-r from-background to-transparent pointer-events-none z-10" />
        <div className="absolute right-0 top-0 bottom-0 w-40 bg-gradient-to-l from-background to-transparent pointer-events-none z-10" />
      </div>

      <div className="mt-8 flex items-center justify-center gap-4 text-sm text-foreground/40">
        <ArrowLeft className="w-4 h-4 animate-pulse" />
        <span>Scroll or hover to pause</span>
        <ArrowRight className="w-4 h-4 animate-pulse" />
      </div>
    </div>
  );
}

export function WhyChooseUs() {
  return (
    <section className="py-20 bg-background relative overflow-hidden">
      {/* Background Gradient */}
      <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background pointer-events-none" />

      <Container className="relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl md:text-4xl font-bold mb-4 tracking-tight leading-tight"
          >
            Why Choose Advisync AI?
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-white/70 max-w-2xl mx-auto leading-relaxed"
          >
            We combine AI voice technology expertise with workflow automation knowledge to deliver
            exceptional results for Australian businesses seeking 24/7 customer service solutions.
          </motion.p>
        </div>

        {/* Feature Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-6 mb-24">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              className="relative group"
              whileHover={{ translateY: -5 }}
              transition={{ type: "spring", stiffness: 300 }}
            >
              <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              <div className="h-full p-8 rounded-2xl bg-secondary/5 border border-secondary/10 relative backdrop-blur-sm">
                {/* Large Background Icon */}
                <div className="absolute top-4 right-4 text-primary/5 pointer-events-none">
                  <feature.icon className="w-24 h-24 transform -rotate-12" />
                </div>

                {/* Main Content */}
                <div className="relative">
                  {/* Icon Container */}
                  <div className="w-12 h-12 rounded-xl bg-primary/10 flex items-center justify-center mb-6 transform group-hover:scale-110 transition-transform duration-300">
                    <feature.icon className="w-6 h-6 text-primary" />
                  </div>

                  {/* Title and Description */}
                  <h3 className="text-xl font-semibold mb-3">{feature.title}</h3>
                  <p className="text-foreground/60 mb-6 min-h-[48px]">
                    {feature.description}
                  </p>

                  {/* Stats */}
                  <div className="flex items-center gap-2">
                    <span className="text-2xl font-bold text-accent-secondary">{feature.stats.value}</span>
                    <span className="text-sm text-foreground/60">{feature.stats.label}</span>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <TestimonialCarousel />
      </Container>
    </section>
  );
}