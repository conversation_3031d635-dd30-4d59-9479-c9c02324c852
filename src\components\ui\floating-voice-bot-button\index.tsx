'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mic, <PERSON>, Bot, Phone } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FloatingVoiceBotButtonProps {
  position?: 'bottom-right' | 'bottom-left';
  label?: string;
  className?: string;
  publicKey?: string;
  assistantId?: string;
}

export function FloatingVoiceBotButton({
  position = 'bottom-left',
  label = 'Get Instant Quote',
  className,
  publicKey,
  assistantId
}: FloatingVoiceBotButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false)
  const [isConnecting, setIsConnecting] = useState(false)

  const handleStartCall = async () => {
    setIsConnecting(true)
    try {
      // Call the webhook to initiate the AI assistant call
      const response = await fetch('https://n8n.tmai.com.au/webhook/twilio-retell-handler', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'start_demo_call',
          source: 'website_floating_button',
          timestamp: new Date().toISOString()
        })
      })

      if (response.ok) {
        // Show success message or handle the response
        console.log('AI Assistant call initiated successfully')
      } else {
        console.error('Failed to initiate AI Assistant call')
      }
    } catch (error) {
      console.error('Error initiating AI Assistant call:', error)
    } finally {
      setIsConnecting(false)
    }
  };

  // Show button after scrolling down a bit
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      setIsVisible(scrollY > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Position classes
  const positionClasses = {
    'bottom-right': 'right-4 sm:right-6 bottom-20 sm:bottom-24',
    'bottom-left': 'left-4 sm:left-6 bottom-20 sm:bottom-24',
  };

  return (
    <>
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className={cn(
              "fixed z-50 flex flex-col items-end",
              positionClasses[position],
              className
            )}
          >
            {/* Popup Voice Bot Widget */}
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9, y: 10 }}
                  animate={{ opacity: 1, scale: 1, y: 0 }}
                  exit={{ opacity: 0, scale: 0.9, y: 10 }}
                  className="bg-background/30 backdrop-blur-sm border border-accent-secondary/20 rounded-xl shadow-xl shadow-accent-secondary/20 mb-4 w-[350px] sm:w-[400px] overflow-hidden"
                >
                  <div className="flex items-center justify-between p-4 border-b border-accent-secondary/20">
                    <h3 className="font-bold text-white flex items-center gap-3 text-lg">
                      <div className="w-8 h-8 rounded-full bg-accent-secondary/20 flex items-center justify-center">
                        <Bot size={20} className="text-accent-secondary" />
                      </div>
                      <span>Speak With Your AI Business Assistant</span>
                    </h3>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="p-1 rounded-full hover:bg-accent-secondary/20 text-white/60 hover:text-accent-secondary transition-colors"
                      aria-label="Close voice bot"
                    >
                      <X size={18} />
                    </button>
                  </div>
                  <div className="p-6">
                    <div className="text-center mb-8">
                      <p className="text-white/95 text-lg leading-relaxed mb-6">
                        Get an instant quote for your AI business assistant.
                        <span className="text-accent-secondary font-bold">Perfect for tradies, healthcare providers, and NDIS services.</span>
                      </p>

                      <div className="bg-gradient-to-r from-accent-secondary/10 to-accent-secondary/5 rounded-2xl p-6 mb-6 border border-accent-secondary/30">
                        <div className="flex items-center gap-3 mb-4">
                          <div className="w-10 h-10 rounded-full bg-accent-secondary/20 flex items-center justify-center">
                            <span className="text-accent-secondary text-lg">💼</span>
                          </div>
                          <p className="text-accent-secondary text-base font-bold">
                            Your AI Business Assistant Will:
                          </p>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-left">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span className="text-white/90 text-sm">Answer calls 24/7</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                            <span className="text-white/90 text-sm">Book appointments instantly</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                            <span className="text-white/90 text-sm">Capture every lead</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                            <span className="text-white/90 text-sm">Send follow-up messages</span>
                          </div>
                        </div>
                      </div>

                      <div className="bg-gradient-to-r from-green-500/10 to-emerald-500/10 rounded-xl p-4 mb-6 border border-green-500/20">
                        <p className="text-green-400 text-sm font-bold mb-2">
                          🚀 Click Below to Speak With Your AI Assistant Demo
                        </p>
                        <p className="text-white/80 text-xs">
                          Experience exactly how your customers will interact with your AI business assistant
                        </p>
                      </div>
                    </div>
                    {/* Voice Assistant Interface */}
                    <div className="bg-gradient-to-br from-bg-primary/60 to-bg-primary/40 rounded-2xl p-8 border border-accent-secondary/30 backdrop-blur-sm">
                      <div className="text-center">
                        <div className="relative w-20 h-20 mx-auto mb-6">
                          <div className="w-20 h-20 bg-gradient-to-br from-accent-secondary/30 to-accent-secondary/10 rounded-full flex items-center justify-center border-2 border-accent-secondary/40">
                            <Mic className="w-10 h-10 text-accent-secondary drop-shadow-lg" />
                          </div>
                          {isConnecting && (
                            <div className="absolute inset-0 rounded-full border-4 border-accent-secondary/30 border-t-accent-secondary animate-spin"></div>
                          )}
                          <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center border-2 border-white">
                            <span className="text-white text-xs font-bold">AI</span>
                          </div>
                        </div>

                        <h4 className="text-white font-bold text-lg mb-2">Ready to Experience Your AI Assistant?</h4>
                        <p className="text-white/80 text-sm mb-6 leading-relaxed">
                          Click below to start a live demo call with your AI business assistant.
                          See how it handles inquiries, books appointments, and captures leads.
                        </p>

                        <button
                          onClick={handleStartCall}
                          disabled={isConnecting}
                          className="w-full bg-gradient-to-r from-accent-secondary to-accent-secondary/90 hover:from-accent-secondary/95 hover:to-accent-secondary/85 text-bg-primary font-bold py-4 px-8 rounded-xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95"
                        >
                          {isConnecting ? (
                            <div className="flex items-center justify-center gap-2">
                              <div className="w-4 h-4 border-2 border-bg-primary/30 border-t-bg-primary rounded-full animate-spin"></div>
                              <span>Connecting Your AI Assistant...</span>
                            </div>
                          ) : (
                            <div className="flex items-center justify-center gap-2">
                              <Phone className="w-5 h-5" />
                              <span>Start Live AI Demo Call</span>
                            </div>
                          )}
                        </button>

                        <p className="text-accent-secondary text-xs mt-4 font-medium">
                          🔒 Secure demo call • No personal information required
                        </p>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Button */}
            <motion.button
              onClick={() => setIsOpen(!isOpen)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={cn(
                "group relative flex items-center gap-3 px-5 py-4 md:px-7 md:py-5",
                "bg-gradient-to-r from-accent-secondary to-accent-secondary/90 text-bg-primary rounded-2xl shadow-xl hover:shadow-2xl",
                "transition-all duration-500 hover:from-accent-secondary/95 hover:to-accent-secondary/85",
                "border-2 border-accent-secondary/30 hover:border-accent-secondary/50",
                "backdrop-blur-sm hover:backdrop-blur-md",
                isOpen && "from-accent-secondary/90 to-accent-secondary/80 shadow-2xl"
              )}
            >
              {isOpen ? (
                <>
                  <X className="w-6 h-6 md:w-7 md:h-7 drop-shadow-sm" />
                  <span className="hidden sm:block font-bold text-sm md:text-base tracking-wide drop-shadow-sm">Close</span>
                </>
              ) : (
                <>
                  <div className="relative">
                    <Mic className="w-6 h-6 md:w-7 md:h-7 drop-shadow-sm" />
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full animate-pulse border-2 border-white"></div>
                  </div>
                  <span className="hidden sm:block font-bold text-sm md:text-base tracking-wide drop-shadow-sm">{label}</span>
                </>
              )}

              {/* Enhanced glow effect */}
              <div className="absolute inset-0 rounded-2xl bg-accent-secondary/20 blur-xl -z-10 group-hover:bg-accent-secondary/30 transition-all duration-500"></div>

              {/* Pulse animation */}
              {!isOpen && (
                <div className="absolute inset-0 rounded-2xl bg-accent-secondary/15 animate-ping"></div>
              )}

              {/* Shimmer effect */}
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 group-hover:animate-shimmer"></div>
            </motion.button>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}
