'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { ServiceModal } from './service-modal';

interface ServiceCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  href: string;
  features?: string[];
  benefits?: string[];
  bookingUrl?: string;
  className?: string;
}

export function ServiceCard({
  title,
  description,
  icon: Icon,
  href,
  features,
  benefits,
  bookingUrl,
  className,
}: ServiceCardProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <motion.div
        whileHover={{ y: -5 }}
        whileTap={{ scale: 0.98 }}
        className={cn(
          "group relative overflow-hidden rounded-xl sm:rounded-2xl bg-background/50 border border-border/50 p-4 sm:p-5 md:p-6 transition-colors hover:border-primary/50 cursor-pointer h-full",
          className
        )}
        onClick={() => setIsModalOpen(true)}
      >
        <div className="relative z-10 space-y-3 sm:space-y-4">
          <div className="mb-1 sm:mb-2 inline-flex h-10 w-10 sm:h-12 sm:w-12 items-center justify-center rounded-lg bg-primary/10 text-primary group-hover:bg-accent-secondary group-hover:text-bg-primary transition-colors">
            <Icon size={20} className="sm:w-6 sm:h-6" />
          </div>
          <h3 className="text-lg sm:text-xl font-semibold text-foreground group-hover:text-accent-secondary transition-colors">
            {title}
          </h3>
          <p className="text-sm sm:text-base text-foreground/60">
            {description}
          </p>
        </div>
        <div className="absolute inset-0 z-0 bg-gradient-to-br from-primary/0 via-primary/0 to-primary/0 opacity-0 group-hover:opacity-10 transition-opacity" />
      </motion.div>

      <ServiceModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        service={{
          title,
          description,
          features,
          benefits,
          bookingUrl,
        }}
      />
    </>
  );
} 