'use client';

import { useEffect, useRef } from 'react';
import { cn } from '@/lib/utils';

interface CalendlyWidgetProps {
  url?: string;
  height?: number;
  className?: string;
}

export function CalendlyWidget({ 
  url = 'https://calendly.com/advisync/15min',
  height = 600,
  className 
}: CalendlyWidgetProps) {
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Load Calendly widget script
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    
    script.onload = () => {
      if (containerRef.current && (window as any).Calendly) {
        (window as any).Calendly.initInlineWidget({
          url,
          parentElement: containerRef.current,
          prefill: {},
          utm: {}
        });
      }
    };

    document.head.appendChild(script);

    return () => {
      // Cleanup script when component unmounts
      const existingScript = document.querySelector('script[src="https://assets.calendly.com/assets/external/widget.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, [url]);

  return (
    <div 
      ref={containerRef}
      className={cn(
        "calendly-inline-widget",
        "w-full rounded-lg overflow-hidden",
        "border border-primary/20 bg-background",
        className
      )}
      style={{ minWidth: '320px', height: `${height}px` }}
      data-url={url}
    />
  );
}

// Alternative popup trigger component
interface CalendlyPopupProps {
  url?: string;
  text?: string;
  className?: string;
  children?: React.ReactNode;
}

export function CalendlyPopup({ 
  url = 'https://calendly.com/advisync/15min',
  text = 'Schedule time with me',
  className,
  children 
}: CalendlyPopupProps) {
  useEffect(() => {
    // Load Calendly widget script
    const script = document.createElement('script');
    script.src = 'https://assets.calendly.com/assets/external/widget.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      const existingScript = document.querySelector('script[src="https://assets.calendly.com/assets/external/widget.js"]');
      if (existingScript) {
        document.head.removeChild(existingScript);
      }
    };
  }, []);

  const openCalendly = () => {
    if ((window as any).Calendly) {
      (window as any).Calendly.initPopupWidget({ url });
    } else {
      // Fallback to direct link
      window.open(url, '_blank');
    }
  };

  if (children) {
    return (
      <div onClick={openCalendly} className={className}>
        {children}
      </div>
    );
  }

  return (
    <button
      onClick={openCalendly}
      className={cn(
        "inline-flex items-center justify-center px-6 py-3",
        "bg-primary text-white rounded-lg hover:bg-primary/90",
        "transition-colors duration-200",
        className
      )}
    >
      {text}
    </button>
  );
}
