import { NextResponse } from 'next/server';
import { z } from 'zod';
import { sendContactFormToN8N } from '@/lib/n8n';

// Validation schema
const contactSchema = z.object({
  name: z.string().min(2),
  email: z.string().email(),
  company: z.string().min(2),
  phone: z.string().min(10),
  teamSize: z.string().optional(),
  contactPreference: z.string().optional(),
  service: z.string(),
  message: z.string().min(10),
});

export async function POST(request: Request) {
  try {
    const data = await request.json();

    // Validate the data
    const validatedData = contactSchema.parse(data);

    // Send data to N8N workflow
    const n8nResult = await sendContactFormToN8N(validatedData);

    if (!n8nResult.success) {
      console.error('N8N webhook failed:', n8nResult.error);
      // Continue with success response - don't block user experience
    }

    return NextResponse.json({
      success: true,
      message: 'Form submitted successfully',
    });
  } catch (error) {
    console.error('Form submission error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          errors: error.errors 
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { 
        success: false,
        message: 'Internal server error' 
      },
      { status: 500 }
    );
  }
} 