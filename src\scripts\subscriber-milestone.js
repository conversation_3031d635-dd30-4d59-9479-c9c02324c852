const fs = require('fs');
const path = require('path');
const nodemailer = require('nodemailer');
require('dotenv').config({ path: path.resolve(process.cwd(), '.env.local') });

// Email configuration
const EMAIL_USER = process.env.EMAIL_USER;
const EMAIL_PASS = process.env.EMAIL_PASS;
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || EMAIL_USER;

// Path to subscriber data file
const subscribersPath = path.join(process.cwd(), 'subscribers.json');
const lastMilestonePath = path.join(process.cwd(), 'last-milestone.json');

// Configure nodemailer
const transporter = nodemailer.createTransport({
  service: 'gmail',
  auth: {
    user: EMAIL_USER,
    pass: EMAIL_PASS
  }
});

// Function to read subscribers
function getSubscribers() {
  try {
    if (fs.existsSync(subscribersPath)) {
      const data = fs.readFileSync(subscribersPath, 'utf8');
      console.log('Subscriber file content:', data.substring(0, 100) + (data.length > 100 ? '...' : ''));
      
      // Handle empty file
      if (!data || data.trim() === '') {
        console.log('Subscriber file is empty or contains only whitespace');
        return [];
      }
      
      try {
        const parsed = JSON.parse(data);
        // Check if the parsed data is an array
        if (Array.isArray(parsed)) {
          return parsed;
        } else {
          console.error('Subscriber data is not an array:', typeof parsed);
          return [];
        }
      } catch (parseError) {
        console.error('Error parsing subscriber JSON:', parseError.message);
        return [];
      }
    } else {
      console.log('Subscriber file does not exist:', subscribersPath);
      // Create empty subscribers file
      fs.writeFileSync(subscribersPath, JSON.stringify([]));
      console.log('Created empty subscribers file');
      return [];
    }
  } catch (error) {
    console.error('Error reading subscribers:', error.message);
    return [];
  }
}

// Function to get last milestone
function getLastMilestone() {
  try {
    if (fs.existsSync(lastMilestonePath)) {
      return JSON.parse(fs.readFileSync(lastMilestonePath, 'utf8')).milestone || 0;
    }
    return 0;
  } catch (error) {
    console.error('Error reading last milestone:', error);
    return 0;
  }
}

// Function to save last milestone
function saveLastMilestone(milestone) {
  try {
    fs.writeFileSync(lastMilestonePath, JSON.stringify({ milestone }));
  } catch (error) {
    console.error('Error saving milestone:', error);
  }
}

// Generate CSV content from subscribers
function generateCSV(subscribers) {
  const headers = 'Email,Subscription Date\n';
  const rows = subscribers.map(sub => `${sub.email},${sub.subscribedAt}`).join('\n');
  return headers + rows;
}

// Send email with CSV attachment
async function sendEmail(subscribers, milestone) {
  const csvContent = generateCSV(subscribers);
  const date = new Date().toISOString().split('T')[0];
  const fileName = `subscribers-${date}-milestone-${milestone}.csv`;
  
  const mailOptions = {
    from: EMAIL_USER,
    to: ADMIN_EMAIL,
    subject: `🎉 Newsletter Milestone: ${milestone} Subscribers Reached!`,
    html: `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4338ca;">Milestone Reached: ${milestone} Subscribers! 🎉</h2>
        <p>Your newsletter has now reached <strong>${milestone} subscribers</strong>. Congratulations!</p>
        <p>Attached is a CSV file containing all your current subscribers.</p>
        <p>Subscriber growth summary:</p>
        <ul>
          <li>Current total: <strong>${subscribers.length} subscribers</strong></li>
          <li>Last milestone: <strong>${milestone - 10} subscribers</strong></li>
          <li>Growth: <strong>+10 subscribers</strong></li>
        </ul>
        <p>The CSV file contains email addresses and subscription dates for your records.</p>
        <hr style="border: 1px solid #eee; margin: 20px 0;">
        <p style="color: #666; font-size: 12px;">This is an automated message sent by your website's subscriber milestone system.</p>
      </div>
    `,
    attachments: [
      {
        filename: fileName,
        content: csvContent
      }
    ]
  };

  try {
    await transporter.sendMail(mailOptions);
    console.log(`Milestone email sent: ${milestone} subscribers`);
    return true;
  } catch (error) {
    console.error('Error sending milestone email:', error);
    return false;
  }
}

// Main function
async function checkMilestone() {
  console.log('Checking subscriber milestones...');
  console.log('Subscribers file path:', subscribersPath);
  
  // Get subscribers and current count
  const subscribers = getSubscribers();
  const count = Array.isArray(subscribers) ? subscribers.length : 0;
  
  console.log(`Subscribers count: ${count}`);
  console.log(`Subscriber data type: ${typeof subscribers}`);
  
  // Get last milestone we notified about
  const lastMilestone = getLastMilestone();
  
  // Calculate current milestone (rounded down to nearest 10)
  const currentMilestone = Math.floor(count / 10) * 10;
  
  console.log(`Last milestone: ${lastMilestone}`);
  console.log(`Current milestone: ${currentMilestone}`);
  
  // If we've reached a new milestone of at least 10
  if (currentMilestone >= 10 && currentMilestone > lastMilestone) {
    console.log(`New milestone reached: ${currentMilestone}!`);
    
    // Send milestone email
    const success = await sendEmail(subscribers, currentMilestone);
    
    // If email sent successfully, update the last milestone
    if (success) {
      saveLastMilestone(currentMilestone);
      console.log(`Updated last milestone to ${currentMilestone}`);
    }
  } else {
    console.log('No new milestone reached.');
  }
}

// Run the script
checkMilestone().catch(console.error);

// Export for potential programmatic use
module.exports = { checkMilestone }; 