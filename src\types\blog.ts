export interface Author {
  name: string;
  role: string;
  image?: string;
  imageWebp?: string;
  imageDimensions?: {
    width: number;
    height: number;
  };
  bio?: string;
  socialLinks?: {
    twitter?: string;
    linkedin?: string;
    website?: string;
  };
}

export interface BlogImage {
  url: string;
  urlWebp?: string;
  alt: string;
  caption: string;
  width?: number;
  height?: number;
  priority?: boolean;
}

export interface TableOfContentsItem {
  title: string;
  id: string;
  subItems?: Array<{
    title: string;
    id: string;
  }>;
}

export interface BlogSection {
  id: string;
  title: string;
  content: string;
  images?: BlogImage[];
}

export interface BlogPost {
  id: string;
  title: string;
  description: string;
  content?: string;
  image: string;
  imageWebp?: string;
  imageDimensions?: {
    width: number;
    height: number;
  };
  date: string;
  readTime: string;
  author: Author;
  category: string;
  tags: string[];
  featured?: boolean;
  status: 'draft' | 'published' | 'archived';
  tableOfContents?: {
    title: string;
    items: TableOfContentsItem[];
  };
  sections?: BlogSection[];
  seoMetadata?: {
    title?: string;
    description?: string;
    keywords?: string[];
    canonicalUrl?: string;
    schema?: {
      "@context": string;
      "@type": string;
      [key: string]: any;
    };
  };
  relatedPosts?: string[];
  lastUpdated?: string;
  additionalImages?: BlogImage[];
}

export interface BlogCategory {
  id: string;
  name: string;
  description: string;
} 