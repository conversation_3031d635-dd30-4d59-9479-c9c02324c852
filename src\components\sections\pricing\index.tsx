'use client';

import { motion } from 'framer-motion';
import { CheckCircle, Star, ArrowRight, Bot, Zap, MessageSquare, Sparkles } from 'lucide-react';
import { Container } from '@/components/common/container';
import { Button } from '@/components/common/button';
import Link from 'next/link';

// AI Voice Agent Pricing Plans
const voiceAgentPlans = [
  {
    name: 'Voice Starter',
    description: 'Perfect for small businesses getting started with AI voice agents',
    price: '1,999',
    popular: false,
    features: [
      '24/7 AI voice receptionist',
      'Basic call handling & routing',
      'Appointment booking integration',
      'Lead capture & qualification',
      'Email & SMS notifications',
      'Australian accent & terminology',
      'Basic CRM integration',
      '3 months support & training'
    ],
    benefits: [
      'Never miss a call again',
      'Reduce staff workload',
      'Capture more leads',
      'Professional customer experience'
    ]
  },
  {
    name: 'Voice Professional',
    description: 'Comprehensive AI voice solution for growing businesses',
    price: '3,999',
    popular: true,
    features: [
      'Advanced AI voice receptionist',
      'Intelligent call routing & escalation',
      'Multi-calendar appointment booking',
      'Advanced lead scoring & qualification',
      'Automated follow-up sequences',
      'Custom voice personality & scripts',
      'Full CRM & workflow integration',
      'Real-time analytics & reporting',
      'Priority support & monthly optimization',
      '6 months support & training'
    ],
    benefits: [
      'Increase conversion rates by 40%',
      'Save 15+ hours per week',
      'Professional brand presence',
      'Scalable customer service'
    ]
  },
  {
    name: 'Voice Enterprise',
    description: 'Complete AI voice & automation ecosystem for established businesses',
    price: '6,999',
    popular: false,
    features: [
      'Multi-agent AI voice system',
      'Advanced conversation flows',
      'Department-specific routing',
      'Intelligent appointment management',
      'Advanced lead nurturing workflows',
      'Custom integrations & APIs',
      'Multi-language support',
      'Advanced analytics & insights',
      'White-label voice solutions',
      'Dedicated account manager',
      '12 months support & optimization'
    ],
    benefits: [
      'Handle unlimited call volume',
      'Enterprise-grade reliability',
      'Custom business logic',
      'Complete automation ecosystem'
    ]
  }
];

// Workflow Automation Add-ons
const automationAddons = [
  {
    name: 'Basic Automation',
    description: 'Essential workflow automation for small businesses',
    price: '999',
    features: [
      'Email automation',
      'Basic scheduling workflows',
      'Document templates',
      'Simple integrations',
      '3 months support'
    ]
  },
  {
    name: 'Advanced Automation',
    description: 'Comprehensive automation suite',
    price: '1,999',
    features: [
      'Advanced workflow automation',
      'Multi-system integrations',
      'Custom business logic',
      'Analytics & reporting',
      '6 months support'
    ]
  }
];

export function PricingSection() {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-secondary/10 rounded-full blur-3xl" />
      </div>

      <Container>
        <div className="relative px-4 sm:px-6 md:px-0">
          {/* Section Header */}
          <div className="text-center mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-primary/20 border border-accent-secondary/30"
            >
              <Bot className="w-5 h-5 text-accent-secondary" />
              <span className="text-sm font-medium text-accent-secondary">AI Voice Agent Pricing</span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 tracking-tight leading-tight"
            >
              Simple, Transparent Pricing
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="text-foreground/70 max-w-3xl mx-auto leading-relaxed text-lg"
            >
              Choose the perfect AI voice agent solution for your Australian business. 
              All plans include setup, training, and ongoing support.
            </motion.p>
          </div>

          {/* AI Voice Agent Plans */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 md:gap-8 mb-12 md:mb-16">
            {voiceAgentPlans.map((plan, index) => (
              <motion.div
                key={plan.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className={`relative p-6 md:p-8 rounded-2xl border transition-all duration-300 hover:shadow-lg ${
                  plan.popular
                    ? 'bg-primary/5 border-accent-secondary shadow-glow-gold scale-105'
                    : 'bg-background/50 border-border/50 hover:border-primary/30'
                }`}
              >
                {plan.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <div className="bg-accent-secondary text-bg-primary px-4 py-1 rounded-full text-sm font-medium flex items-center gap-1">
                      <Star className="w-4 h-4 fill-current" />
                      Most Popular
                    </div>
                  </div>
                )}

                <div className="text-center mb-6">
                  <h3 className="text-xl md:text-2xl font-bold mb-2">{plan.name}</h3>
                  <p className="text-foreground/60 mb-4">{plan.description}</p>
                  <div className="mb-4">
                    <span className="text-3xl md:text-4xl font-bold text-accent-secondary">${plan.price}</span>
                    <span className="text-foreground/60"> AUD</span>
                  </div>
                </div>

                <ul className="space-y-3 mb-8">
                  {plan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-secondary flex-shrink-0 mt-0.5" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button 
                  asChild
                  variant={plan.popular ? "secondary" : "default"}
                  size="responsive-lg"
                  className="w-full"
                >
                  <Link href="/consultation">
                    <span>Get Started</span>
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Automation Add-ons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              <span className="text-accent-secondary">+</span> Workflow Automation Add-ons
            </h3>
            <p className="text-foreground/60 max-w-2xl mx-auto">
              Enhance your AI voice agent with powerful workflow automation to create a complete business solution.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8 max-w-4xl mx-auto mb-12">
            {automationAddons.map((addon, index) => (
              <motion.div
                key={addon.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="p-6 bg-background/50 border border-border/50 rounded-xl hover:border-primary/30 transition-colors"
              >
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 rounded-lg bg-accent-secondary/10 flex items-center justify-center">
                    <Zap className="w-5 h-5 text-accent-secondary" />
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold">{addon.name}</h4>
                    <p className="text-sm text-foreground/60">{addon.description}</p>
                  </div>
                </div>
                
                <div className="mb-4">
                  <span className="text-2xl font-bold text-accent-secondary">${addon.price}</span>
                  <span className="text-foreground/60"> AUD</span>
                </div>

                <ul className="space-y-2 mb-6">
                  {addon.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-2 text-sm">
                      <CheckCircle className="w-4 h-4 text-accent-secondary flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button variant="outline" size="sm" className="w-full">
                  Add to Plan
                </Button>
              </motion.div>
            ))}
          </div>

          {/* CTA Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center bg-gradient-to-r from-primary/10 via-accent-secondary/10 to-primary/10 rounded-2xl p-8 md:p-12"
          >
            <div className="inline-flex items-center gap-2 mb-4">
              <Sparkles className="w-6 h-6 text-accent-secondary" />
              <span className="text-accent-secondary font-semibold">Ready to Transform Your Business?</span>
            </div>
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Book Your Free Consultation Today
            </h3>
            <p className="text-foreground/70 mb-6 max-w-2xl mx-auto">
              Speak with our AI automation experts to find the perfect solution for your business. 
              No obligations, just practical advice tailored to your needs.
            </p>
            <Button 
              asChild
              variant="secondary"
              size="responsive-lg"
              className="shadow-glow-gold"
            >
              <Link href="/consultation">
                <MessageSquare className="w-5 h-5 mr-2" />
                <span>Free Consultation</span>
              </Link>
            </Button>
          </motion.div>
        </div>
      </Container>
    </section>
  );
}
