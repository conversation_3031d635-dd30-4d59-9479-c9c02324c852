'use client';

import { motion } from 'framer-motion';
import { CheckCircle, Star, ArrowRight, Bot, Zap, MessageSquare, Sparkles } from 'lucide-react';
import { Container } from '@/components/common/container';
import { Button } from '@/components/common/button';
import Link from 'next/link';

// <PERSON> Single Plan Approach
const mainPlan = {
  name: 'Complete Business Assistant',
  description: 'Professional reception for $10 a day - Save $57,000 annually vs hiring a receptionist',
  monthlyPrice: '299',
  setupPrice: '299',
  features: [
    '200 professional calls per month (3 minutes each)',
    '24/7 Australian AI receptionist',
    'Professional email confirmations for all interactions',
    'Lead capture automatically saved to CRM',
    'Appointment booking with calendar sync',
    'Professional message and callback handling',
    'Complete setup and training included',
    'No contracts - cancel with 30 days notice'
  ],
  valueProps: [
    'Never miss another call or customer',
    'No sick days, holidays, or breaks',
    'Professional Australian voice',
    'Complete interaction records via email'
  ]
};

// Workflow Add-ons ($49/month each)
const workflowAddons = [
  {
    name: 'Quote Request Processor',
    description: 'Automatically handle and process quote requests',
    price: '49',
    features: [
      'Automated quote request capture',
      'Customer information collection',
      'Quote follow-up sequences',
      'Integration with pricing systems'
    ]
  },
  {
    name: 'Customer Service Automation',
    description: 'Handle common customer service inquiries',
    price: '49',
    features: [
      'FAQ automation',
      'Order status inquiries',
      'Return and refund processing',
      'Escalation to human agents'
    ]
  },
  {
    name: 'Emergency Protocol Setup',
    description: 'Handle urgent calls with special protocols',
    price: '49',
    features: [
      'Emergency call detection',
      'Priority routing',
      'After-hours emergency handling',
      'Immediate notification system'
    ]
  },
  {
    name: 'Multi-Location Call Routing',
    description: 'Route calls to appropriate locations',
    price: '49',
    features: [
      'Location-based routing',
      'Multi-branch support',
      'Regional appointment booking',
      'Location-specific information'
    ]
  }
];

export function PricingSection() {
  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-b from-background via-primary/5 to-background" />
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent-secondary/10 rounded-full blur-3xl" />
      </div>

      <Container>
        <div className="relative px-4 sm:px-6 md:px-0">
          {/* Section Header */}
          <div className="text-center mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="inline-flex items-center gap-2 px-4 py-2 mb-6 rounded-full bg-primary/20 border border-accent-secondary/30"
            >
              <Bot className="w-5 h-5 text-accent-secondary" />
              <span className="text-sm font-medium text-accent-secondary">Warren Buffett Pricing Philosophy</span>
            </motion.div>

            <motion.h2
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.1 }}
              className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 tracking-tight leading-tight"
            >
              Professional Reception for <span className="text-accent-secondary">$10 a Day</span>
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.2 }}
              className="text-foreground/70 max-w-3xl mx-auto leading-relaxed text-lg mb-6"
            >
              Save $57,000 annually vs hiring a receptionist. One perfect plan at a fair price
              that customers would gladly pay twice as much for.
            </motion.p>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: 0.3 }}
              className="flex flex-wrap justify-center gap-6 text-sm text-foreground/60"
            >
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>No sick days or holidays</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Professional Australian voice</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-accent-secondary rounded-full"></div>
                <span>Complete interaction records</span>
              </div>
            </motion.div>
          </div>

          {/* Single Main Plan */}
          <div className="max-w-2xl mx-auto mb-12 md:mb-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              className="relative p-8 md:p-10 rounded-3xl border-2 border-accent-secondary bg-gradient-to-br from-primary/5 via-accent-secondary/5 to-primary/5 shadow-2xl shadow-accent-secondary/20"
            >
              {/* Popular Badge */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-accent-secondary text-bg-primary px-6 py-2 rounded-full text-sm font-bold flex items-center gap-2">
                  <Star className="w-4 h-4 fill-current" />
                  Essential Business Tool
                </div>
              </div>

              <div className="text-center mb-8">
                <h3 className="text-2xl md:text-3xl font-bold mb-3">{mainPlan.name}</h3>
                <p className="text-foreground/70 mb-6 text-lg">{mainPlan.description}</p>

                {/* Pricing Display */}
                <div className="bg-background/50 rounded-2xl p-6 mb-6 border border-accent-secondary/20">
                  <div className="flex items-center justify-center gap-8 mb-4">
                    <div className="text-center">
                      <div className="text-3xl md:text-4xl font-bold text-accent-secondary">${mainPlan.monthlyPrice}</div>
                      <div className="text-sm text-foreground/60">per month</div>
                    </div>
                    <div className="text-2xl text-foreground/40">+</div>
                    <div className="text-center">
                      <div className="text-3xl md:text-4xl font-bold text-accent-secondary">${mainPlan.setupPrice}</div>
                      <div className="text-sm text-foreground/60">setup (one-time)</div>
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-lg font-semibold text-green-400 mb-1">Save $57,000+ vs Human Receptionist</div>
                    <div className="text-sm text-foreground/60">Based on $60,000/year salary + benefits</div>
                  </div>
                </div>
              </div>

              {/* Features List */}
              <div className="mb-8">
                <h4 className="text-lg font-semibold mb-4 text-center">Everything Included:</h4>
                <ul className="space-y-3">
                  {mainPlan.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-accent-secondary flex-shrink-0 mt-0.5" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* CTA Buttons */}
              <div className="space-y-3">
                <Button
                  asChild
                  variant="secondary"
                  size="responsive-lg"
                  className="w-full shadow-glow-gold"
                >
                  <Link href="/consultation">
                    <span>Get Started</span>
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="responsive-lg"
                  className="w-full"
                >
                  <Link href="/demo">
                    <span>Book Live Demo</span>
                  </Link>
                </Button>
              </div>
            </motion.div>
          </div>

          {/* Workflow Add-ons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center mb-8"
          >
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              <span className="text-accent-secondary">+</span> Workflow Add-ons
            </h3>
            <p className="text-foreground/60 max-w-2xl mx-auto mb-2">
              Expand your AI assistant with additional business workflows at $49/month each.
            </p>
            <p className="text-sm text-accent-secondary font-medium">
              Custom workflows available - speak to our team about your specific needs
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 max-w-6xl mx-auto mb-12">
            {workflowAddons.map((addon, index) => (
              <motion.div
                key={addon.name}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: index * 0.1 }}
                className="p-5 bg-background/50 border border-border/50 rounded-xl hover:border-accent-secondary/30 transition-all duration-300 hover:shadow-lg"
              >
                <div className="text-center mb-4">
                  <div className="w-12 h-12 mx-auto mb-3 rounded-lg bg-accent-secondary/10 flex items-center justify-center">
                    <Zap className="w-6 h-6 text-accent-secondary" />
                  </div>
                  <h4 className="text-lg font-semibold mb-2">{addon.name}</h4>
                  <p className="text-sm text-foreground/60 mb-3">{addon.description}</p>

                  <div className="mb-4">
                    <span className="text-2xl font-bold text-accent-secondary">${addon.price}</span>
                    <span className="text-foreground/60 text-sm">/month</span>
                  </div>
                </div>

                <ul className="space-y-2 mb-6 text-left">
                  {addon.features.map((feature, idx) => (
                    <li key={idx} className="flex items-start gap-2 text-xs">
                      <CheckCircle className="w-3 h-3 text-accent-secondary flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                <Button variant="outline" size="sm" className="w-full text-xs">
                  Add Workflow
                </Button>
              </motion.div>
            ))}
          </div>

          {/* Trust Building CTA */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-center bg-gradient-to-r from-primary/10 via-accent-secondary/10 to-primary/10 rounded-2xl p-8 md:p-12"
          >
            <div className="inline-flex items-center gap-2 mb-4">
              <Sparkles className="w-6 h-6 text-accent-secondary" />
              <span className="text-accent-secondary font-semibold">Trusted by 200+ Australian Businesses</span>
            </div>
            <h3 className="text-2xl md:text-3xl font-bold mb-4">
              Never Miss Another Call or Customer
            </h3>
            <p className="text-foreground/70 mb-6 max-w-2xl mx-auto">
              See it working in real-time with our live demo, or speak to businesses like yours.
              All costs included - no surprises. Handling calls within 24 hours.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
              <Button
                asChild
                variant="secondary"
                size="responsive-lg"
                className="shadow-glow-gold flex-1"
              >
                <Link href="/consultation">
                  <span>Get Started</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="responsive-lg"
                className="flex-1"
              >
                <Link href="/demo">
                  <MessageSquare className="w-4 h-4 mr-2" />
                  <span>Book Demo</span>
                </Link>
              </Button>
            </div>
            <p className="text-xs text-foreground/50 mt-4">
              Questions? Call us - we're here to help
            </p>
          </motion.div>
        </div>
      </Container>
    </section>
  );
}
