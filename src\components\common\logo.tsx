'use client';

import { useEffect, useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/lib/utils';

interface LogoProps {
  className?: string;
  width?: number;
  height?: number;
}

export function Logo({ className, width = 200, height = 65 }: LogoProps) {
  const [logoSize, setLogoSize] = useState({ width, height });

  // Responsive logo sizing based on viewport width
  useEffect(() => {
    const handleResize = () => {
      const vw = window.innerWidth;
      if (vw < 375) {
        setLogoSize({ width: 120, height: 39 });
      } else if (vw < 640) {
        setLogoSize({ width: 150, height: 49 });
      } else if (vw < 768) {
        setLogoSize({ width: 180, height: 59 });
      } else if (vw < 965) {
        // Added new breakpoint to match navbar mobile breakpoint
        setLogoSize({ width: 190, height: 62 });
      } else {
        setLogoSize({ width, height });
      }
    };

    // Initial size
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [width, height]);

  return (
    <Link href="/" className={cn("block", className)}>
      <div
        style={{
          width: logoSize.width,
          height: logoSize.height,
        }}
        className="relative transition-all duration-300 ease-in-out"
      >
        <Image
          src="/images/advisync-logo.png"
          alt="Advisync AI Solutions - Melbourne's Digital Automation Partner"
          fill
          className="object-contain"
          priority
          sizes="(max-width: 375px) 120px, (max-width: 640px) 150px, (max-width: 768px) 180px, (max-width: 965px) 190px, 200px"
          quality={95}
        />
      </div>
    </Link>
  );
}