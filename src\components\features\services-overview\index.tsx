'use client';

import { motion } from 'framer-motion';
import {
  Bo<PERSON>,
  Workflow,
  Heart
} from 'lucide-react';
import { Container } from '@/components/common/container';
import { ServiceCard } from '@/components/ui/service-card';

const services = [
  {
    title: "AI Voice Agents",
    description: "Perfect for tradies, healthcare providers, and NDIS services. 24/7 AI receptionists that never miss a call, book appointments, and handle customer inquiries with authentic Australian voices.",
    icon: Bot,
    href: "/services/ai-voice-agents",
    value: "voice_agents",
    features: [
      "24/7 Call Answering",
      "Appointment Booking",
      "Lead Information Capture",
      "Basic Customer Service",
      "Call Transfer When Needed",
      "SMS Follow-ups"
    ],
    benefits: [
      "Never miss a call again (even when on a job)",
      "Capture leads 24/7 without hiring staff",
      "Professional first impression for every caller",
      "Book appointments while you sleep"
    ],
    bookingUrl: "/consultation#voice-agents"
  },
  {
    title: "AI Workflow Automation",
    description: "Ideal for busy tradies, healthcare practices, and NDIS providers. Simple automation solutions that connect your business tools and eliminate repetitive paperwork.",
    icon: Workflow,
    href: "/services/business-automation",
    value: "workflow_automation",
    features: [
      "Form to Email Automation",
      "Appointment Reminders",
      "Basic Document Generation",
      "Email Follow-ups"
    ],
    benefits: [
      "Save 5-10 hours per week on admin tasks",
      "Reduce manual data entry errors",
      "Improve customer communication",
      "Simple setup that actually works"
    ],
    bookingUrl: "/consultation#automation"
  },
  {
    title: "Customer Management Automation",
    description: "Perfect for service businesses that want to capture every lead and keep customers happy. Automated follow-ups, appointment management, and customer communication that works alongside your AI voice agents.",
    icon: Heart,
    href: "/services/customer-management",
    value: "customer_management",
    features: [
      "Lead Capture & Follow-up",
      "Customer Communication Automation",
      "Appointment & Booking Management",
      "Customer Data Organization"
    ],
    benefits: [
      "Never lose a lead again",
      "Automate customer follow-ups",
      "Increase repeat business by 40%",
      "Turn more inquiries into paying customers"
    ],
    bookingUrl: "/consultation#customer-management"
  }
];

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
    },
  },
};

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
  },
};

export function ServicesOverview() {
  return (
    <section className="py-12 sm:py-16 md:py-20 bg-background/50">
      <Container>
        <div className="text-center mb-8 md:mb-12 px-4">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-2xl sm:text-3xl md:text-4xl font-bold mb-3 md:mb-4 tracking-tight leading-tight"
          >
            AI Voice & Automation Solutions
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2 }}
            className="text-sm sm:text-base text-white/70 max-w-3xl mx-auto leading-relaxed"
          >
            Perfect for <span className="text-accent-secondary font-medium">tradies</span> (electricians, plumbers, builders),
            <span className="text-accent-secondary font-medium"> healthcare providers</span> (chiropractors, physios, dentists),
            and <span className="text-accent-secondary font-medium">NDIS services</span>. Never miss a call, automate paperwork, and provide 24/7 customer service.
          </motion.p>
        </div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8 px-4 max-w-5xl mx-auto"
        >
          {services.map((service, index) => (
            <motion.div
              key={service.href}
              variants={itemVariants}
              viewport={{ once: true }}
            >
              <ServiceCard {...service} />
            </motion.div>
          ))}
        </motion.div>
      </Container>
    </section>
  );
}