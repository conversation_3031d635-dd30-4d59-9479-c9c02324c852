import type { Config } from "tailwindcss";

const config: Config = {
  darkMode: "class",
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      screens: {
        'xs': '480px',
      },
      colors: {
        // Royal Sapphire & Gold Palette
        "bg-primary": "#080A12",
        "bg-alt-grad-0": "#080A12",
        "bg-alt-grad-100": "#101425",
        "surface-card": "#141826",
        "accent-primary": "#1A6BFF",
        "accent-secondary": "#FFCB47",
        "text-heading": "#FFFFFF",
        "text-body": "#E8EDF7",
        "success": "#26E0B8",
        "error": "#FF697A",
        "border-hair": "rgba(255,255,255,0.06)",

        // Legacy mappings for compatibility
        border: "rgba(255,255,255,0.06)",
        input: "#141826",
        ring: "#1A6BFF",
        background: "#080A12",
        foreground: "#DDE2F5",
        primary: {
          DEFAULT: "#1A6BFF",
          foreground: "#FFFFFF",
        },
        secondary: {
          DEFAULT: "#FFCB47",
          foreground: "#080A12",
        },
        muted: {
          DEFAULT: "#141826",
          foreground: "#DDE2F5",
        },
        accent: {
          DEFAULT: "#1A6BFF",
          foreground: "#FFFFFF",
        },
        destructive: {
          DEFAULT: "#FF697A",
          foreground: "#FFFFFF",
        },
        card: {
          DEFAULT: "#141826",
          foreground: "#DDE2F5",
        },
        popover: {
          DEFAULT: "#141826",
          foreground: "#DDE2F5",
        },
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography"),
  ],
};

export default config; 