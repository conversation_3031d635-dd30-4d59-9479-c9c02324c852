<?xml version="1.0" encoding="UTF-8"?>
<svg width="600" height="600" viewBox="0 0 600 600" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background Circle -->
  <circle cx="300" cy="300" r="280" fill="url(#gradient1)" opacity="0.1"/>
  
  <!-- Decorative Elements -->
  <circle cx="300" cy="300" r="200" stroke="url(#gradient2)" stroke-width="2" opacity="0.3"/>
  <circle cx="300" cy="300" r="150" stroke="url(#gradient3)" stroke-width="2" opacity="0.4"/>
  
  <!-- Central Icon -->
  <g transform="translate(200, 200) scale(0.8)">
    <!-- Brain Circuit -->
    <path d="M250 100 C 350 100, 350 200, 250 200" stroke="#6366F1" stroke-width="3" fill="none"/>
    <path d="M250 200 C 150 200, 150 300, 250 300" stroke="#6366F1" stroke-width="3" fill="none"/>
    <circle cx="250" cy="100" r="5" fill="#6366F1"/>
    <circle cx="250" cy="200" r="5" fill="#6366F1"/>
    <circle cx="250" cy="300" r="5" fill="#6366F1"/>
    
    <!-- Data Flow -->
    <g>
      <circle cx="200" cy="150" r="3" fill="#22D3EE">
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite"/>
      </circle>
      <circle cx="300" cy="250" r="3" fill="#22D3EE">
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="0.5s"/>
      </circle>
      <circle cx="200" cy="350" r="3" fill="#22D3EE">
        <animate attributeName="opacity" values="0;1;0" dur="2s" repeatCount="indefinite" begin="1s"/>
      </circle>
    </g>
  </g>
  
  <!-- Floating Elements -->
  <g>
    <!-- Squares -->
    <rect x="100" y="150" width="20" height="20" fill="#6366F1" opacity="0.2">
      <animateTransform
        attributeName="transform"
        type="translate"
        values="0 0; 0 20; 0 0"
        dur="4s"
        repeatCount="indefinite"
      />
    </rect>
    <rect x="480" y="350" width="20" height="20" fill="#22D3EE" opacity="0.2">
      <animateTransform
        attributeName="transform"
        type="translate"
        values="0 0; 0 -20; 0 0"
        dur="4s"
        repeatCount="indefinite"
      />
    </rect>
    
    <!-- Circles -->
    <circle cx="150" cy="450" r="10" fill="#6366F1" opacity="0.2">
      <animate
        attributeName="r"
        values="10;15;10"
        dur="4s"
        repeatCount="indefinite"
      />
    </circle>
    <circle cx="450" cy="150" r="10" fill="#22D3EE" opacity="0.2">
      <animate
        attributeName="r"
        values="10;15;10"
        dur="4s"
        repeatCount="indefinite"
        begin="1s"
      />
    </circle>
  </g>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0" y1="0" x2="600" y2="600">
      <stop offset="0%" stop-color="#6366F1"/>
      <stop offset="100%" stop-color="#22D3EE"/>
    </linearGradient>
    
    <linearGradient id="gradient2" x1="0" y1="300" x2="600" y2="300">
      <stop offset="0%" stop-color="#6366F1"/>
      <stop offset="100%" stop-color="#22D3EE"/>
    </linearGradient>
    
    <linearGradient id="gradient3" x1="300" y1="0" x2="300" y2="600">
      <stop offset="0%" stop-color="#6366F1"/>
      <stop offset="100%" stop-color="#22D3EE"/>
    </linearGradient>
  </defs>
</svg> 