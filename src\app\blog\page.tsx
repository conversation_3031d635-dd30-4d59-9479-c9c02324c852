import type { Metadata } from 'next';
import { Container } from '@/components/common/container';
import { 
  Clock, 
  Tag, 
  ArrowRight,
  Search,
  Filter
} from 'lucide-react';
import { BlogImage } from '@/components/common/blog-image';
import { NewsletterForm } from '@/components/common/newsletter-form';
import { blogPosts, blogCategories } from '@/config/blog-posts';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Business Automation Insights | Advisync Solutions Blog',
  description: 'Stay updated with the latest business automation trends, digital transformation strategies, and success stories. Expert insights for Melbourne businesses and NDIS providers.',
  keywords: [
    'business automation blog',
    'digital transformation insights',
    'NDIS automation tips',
    'Melbourne business technology',
    'automation strategies',
    'business process optimization',
    'digital solutions blog',
    'Melbourne tech insights'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/blog'
  },
  openGraph: {
    title: 'Business Automation Insights | Advisync Solutions Blog',
    description: 'Expert insights on business automation, digital transformation, and success strategies for Melbourne businesses.',
    url: 'https://advisync.com.au/blog',
    type: 'website',
    siteName: 'Advisync Solutions',
    locale: 'en_AU',
    images: [
      {
        url: '/images/og/blog-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync Solutions Blog'
      }
    ]
  }
};

// Add JSON-LD structured data
const getJsonLd = (posts: typeof blogPosts) => ({
  '@context': 'https://schema.org',
  '@type': 'Blog',
  headline: 'Digital Automation Blog | Advisync Solutions',
  description: 'Expert insights on business automation, digital transformation, and technology solutions for Melbourne businesses.',
  publisher: {
    '@type': 'Organization',
    name: 'Advisync Solutions',
    url: 'https://advisync.com.au',
    logo: {
      '@type': 'ImageObject',
      url: 'https://advisync.com.au/images/advisync-logo.png'
    }
  },
  blogPost: posts.map(post => ({
    '@type': 'BlogPosting',
    headline: post.title,
    description: post.description,
    image: post.image,
    datePublished: post.date,
    dateModified: post.lastUpdated || post.date,
    author: {
      '@type': 'Person',
      name: post.author.name,
      description: post.author.bio || undefined,
      image: post.author.image || undefined
    },
    publisher: {
      '@type': 'Organization',
      name: 'Advisync Solutions',
      logo: {
        '@type': 'ImageObject',
        url: 'https://advisync.com.au/images/advisync-logo.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://advisync.com.au/blog/${post.id}`
    },
    keywords: post.tags.join(', ')
  }))
});

export default function BlogPage() {
  return (
    <main className="py-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(getJsonLd(blogPosts)) }}
      />
      <Container>
        {/* Hero Section */}
        <section className="text-center mb-16 relative">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-secondary/5 to-transparent rounded-3xl -z-10" />
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
            Melbourne Digital Insights
          </h1>
          <p className="text-xl text-foreground/60 max-w-3xl mx-auto">
            Practical tips and guides to help Melbourne small businesses and sole traders thrive in the digital age.
          </p>
        </section>

        {/* Search and Filter */}
        <section className="mb-16">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="relative w-full md:w-96">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 w-5 h-5" />
              <input
                type="text"
                placeholder="Search articles..."
                className="w-full pl-10 pr-4 py-2 bg-secondary/5 border border-primary/10 rounded-full focus:outline-none focus:border-primary/20 transition-colors"
              />
            </div>
            <div className="flex items-center gap-2 w-full md:w-auto overflow-x-auto pb-2 md:pb-0">
              {blogCategories.map((category) => (
                <button
                  key={category.id}
                  className="px-4 py-1 whitespace-nowrap rounded-full bg-secondary/5 hover:bg-secondary/10 text-sm transition-colors"
                >
                  {category.name}
                </button>
              ))}
            </div>
          </div>
        </section>

        {/* Featured Post */}
        {blogPosts.filter(post => post.featured).map(post => (
          <section key={post.id} className="mb-24">
            <Link href={`/blog/${post.id}`} className="block">
              <article className="group relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10" />
                <div className="relative bg-secondary/5 backdrop-blur-sm rounded-3xl p-8 border border-primary/10 hover:border-primary/20 transition-all duration-300">
                  <div className="grid md:grid-cols-2 gap-8 items-center">
                    <div className="relative aspect-video rounded-2xl overflow-hidden">
                      <BlogImage
                        src={post.image}
                        alt={post.title}
                        priority={true}
                        size="large"
                        width={800}
                        height={450}
                        className="group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <div>
                      <span className="text-sm font-medium text-primary/80 mb-2 block">
                        Featured Post
                      </span>
                      <h2 className="text-2xl md:text-3xl font-bold mb-4 group-hover:text-primary transition-colors">
                        {post.title}
                      </h2>
                      <p className="text-foreground/60 mb-6">
                        {post.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm text-foreground/40 mb-6">
                        <span className="flex items-center gap-1">
                          <Clock className="w-4 h-4" />
                          {post.readTime}
                        </span>
                        <span className="flex items-center gap-1">
                          <Tag className="w-4 h-4" />
                          {post.category}
                        </span>
                      </div>
                      <div className="group/btn inline-flex items-center gap-2 text-primary">
                        <span>Read Article</span>
                        <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
                      </div>
                    </div>
                  </div>
                </div>
              </article>
            </Link>
          </section>
        ))}

        {/* Blog Grid */}
        <section className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {blogPosts.filter(post => !post.featured).map(post => (
            <Link 
              key={post.id}
              href={`/blog/${post.id}`}
              className="group relative bg-secondary/5 backdrop-blur-sm rounded-2xl overflow-hidden border border-primary/10 hover:border-primary/20 transition-all duration-300 hover:translate-y-[-4px]"
            >
              <div className="relative aspect-video">
                <BlogImage
                  src={post.image}
                  alt={post.title}
                  size="medium"
                  width={600}
                  height={338}
                  className="group-hover:scale-105 transition-transform duration-500"
                />
              </div>
              <div className="p-6">
                <div className="flex items-center gap-4 text-sm text-foreground/40 mb-4">
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    {post.readTime}
                  </span>
                  <span className="flex items-center gap-1">
                    <Tag className="w-4 h-4" />
                    {post.category}
                  </span>
                </div>
                <h3 className="text-xl font-bold mb-3 line-clamp-2 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                <p className="text-foreground/60 mb-6 line-clamp-3">
                  {post.description}
                </p>
                <div className="flex flex-wrap gap-2 mb-6">
                  {post.tags.map((tag: string) => (
                    <span
                      key={tag}
                      className="px-3 py-1 bg-primary/10 rounded-full text-sm text-primary"
                    >
                      {tag}
                    </span>
                  ))}
                </div>
                <div className="flex items-center gap-2 text-primary group-hover:text-primary/80 transition-colors">
                  <span>Read More</span>
                  <ArrowRight className="w-4 h-4 transform transition-transform group-hover:translate-x-1" />
                </div>
              </div>
            </Link>
          ))}
        </section>

        {/* Newsletter Section */}
        <section className="mt-32">
          <NewsletterForm 
            title="Stay Updated with Melbourne Tech Trends" 
            description="Get the latest digital transformation tips and business automation insights for Melbourne businesses delivered directly to your inbox."
          />
        </section>
      </Container>
    </main>
  );
} 