import type { Metadata } from 'next';
import { Container } from '@/components/common/container';
import {
  ArrowUpRight,
  Users,
  TrendingUp,
  Zap,
  Clock,
  BarChart3,
  Target,
  CheckCircle2,
  ArrowRight,
  Bot,
  Calendar,
  Phone,
  DollarSign,
  Star,
  Wrench,
  Heart,
  Shield
} from 'lucide-react';

export const metadata: Metadata = {
  title: 'AI Voice Agent & Automation Success Stories | Advisync Solutions',
  description: 'See how tradies, healthcare providers, and NDIS services are capturing more revenue with our AI voice agents and workflow automations. Real Melbourne business results.',
  keywords: [
    'AI voice agent case studies',
    'tradie automation success stories',
    'healthcare AI receptionist',
    'NDIS automation solutions',
    'electrician AI voice agent',
    'physiotherapy automation',
    'plumber workflow automation',
    'Melbourne AI solutions'
  ],
  alternates: {
    canonical: 'https://advisync.com.au/case-studies'
  },
  openGraph: {
    title: 'Business Automation Success Stories | Advisync Solutions',
    description: 'Real success stories of Melbourne businesses transformed through our automation solutions. See how we help businesses optimize and grow.',
    url: 'https://advisync.com.au/case-studies',
    type: 'website',
    siteName: 'Advisync Solutions',
    locale: 'en_AU',
    images: [
      {
        url: '/images/og/case-studies-og.jpg',
        width: 1200,
        height: 630,
        alt: 'Advisync Solutions Case Studies'
      }
    ]
  }
};

interface CaseStudy {
  id: string;
  title: string;
  subtitle: string;
  description: string;
  challenge: string;
  solution: string;
  approach: {
    title: string;
    description: string;
    icon: React.ElementType;
  }[];
  results: {
    icon: React.ElementType;
    label: string;
    value: string;
  }[];
  testimonial?: {
    quote: string;
    author: string;
    role: string;
  };
  tags: string[];
  gradient: string;
}

const caseStudies: CaseStudy[] = [
  {
    id: 'electrician-emergency-calls',
    title: 'Electrician Emergency Call System',
    subtitle: 'Tradies & AI Voice Agents',
    description: 'Helping a local electrician capture every emergency call and convert after-hours inquiries into high-value jobs.',
    challenge: 'A busy electrician was missing emergency calls after hours, losing high-value jobs to competitors. Customers calling multiple electricians meant first responder got the job.',
    solution: 'Deployed 24/7 AI voice agent with authentic Australian accent to answer all calls, assess urgency, and book emergency or scheduled jobs instantly.',
    approach: [
      {
        title: 'AI Voice Agent Setup',
        description: '24/7 call answering with Australian accent and electrical terminology',
        icon: Bot
      },
      {
        title: 'Emergency Assessment',
        description: 'Smart call routing for urgent vs scheduled electrical work',
        icon: Zap
      },
      {
        title: 'Job Booking Integration',
        description: 'Instant booking with calendar sync and customer notifications',
        icon: Calendar
      }
    ],
    results: [
      {
        icon: Phone,
        label: 'Emergency Calls Captured',
        value: '100%'
      },
      {
        icon: DollarSign,
        label: 'After-Hours Revenue',
        value: '+85%'
      },
      {
        icon: Star,
        label: 'Customer Satisfaction',
        value: '96%'
      }
    ],
    testimonial: {
      quote: "Never missing an emergency call again has been a game-changer. The AI sounds so natural, customers don't even realise it's not me answering.",
      author: "Mark Thompson",
      role: "Licensed Electrician"
    },
    tags: ['Tradies', 'AI Voice Agent', 'Emergency Calls', 'After-Hours'],
    gradient: 'from-[#6366F1] to-[#22D3EE]'
  },
  {
    id: 'physiotherapy-ai-receptionist',
    title: 'Physiotherapy Clinic AI Receptionist',
    subtitle: 'Healthcare & AI Voice Agents',
    description: 'Helping a busy physiotherapy clinic manage appointments and patient communication with AI.',
    challenge: 'A physiotherapy clinic was overwhelmed during peak booking times, missing calls and frustrating patients who needed treatment appointments.',
    solution: 'Implemented AI receptionist to handle appointment bookings, cancellations, and basic patient inquiries with seamless calendar integration.',
    approach: [
      {
        title: 'AI Receptionist Setup',
        description: '24/7 appointment booking with natural conversation flow',
        icon: Bot
      },
      {
        title: 'Calendar Integration',
        description: 'Real-time availability checking and instant booking confirmation',
        icon: Calendar
      },
      {
        title: 'Patient Communication',
        description: 'Automated reminders and basic inquiry handling',
        icon: Users
      }
    ],
    results: [
      {
        icon: Phone,
        label: 'Missed Calls Eliminated',
        value: '95%'
      },
      {
        icon: TrendingUp,
        label: 'Booking Efficiency',
        value: '+60%'
      },
      {
        icon: Users,
        label: 'New Patient Bookings',
        value: '+40%'
      }
    ],
    testimonial: {
      quote: "The AI receptionist has been incredible. Patients can book appointments anytime, and we never miss a call during busy treatment sessions.",
      author: "Dr. Emma Wilson",
      role: "Principal Physiotherapist"
    },
    tags: ['Healthcare', 'AI Receptionist', 'Appointment Booking', 'Patient Care'],
    gradient: 'from-[#EC4899] to-[#8B5CF6]'
  },
  {
    id: 'plumbing-quote-automation',
    title: 'Plumbing Quote Follow-up System',
    subtitle: 'Tradies & Workflow Automation',
    description: 'Helping a plumbing business convert more quotes into jobs through automated follow-ups.',
    challenge: 'A plumbing business was losing potential jobs because quotes weren\'t followed up consistently, and customers chose competitors who stayed in touch.',
    solution: 'Created automated quote follow-up system with SMS reminders, email sequences, and job booking integration.',
    approach: [
      {
        title: 'Quote Tracking',
        description: 'Automated system to track quote status and follow-up timing',
        icon: Target
      },
      {
        title: 'SMS & Email Sequences',
        description: 'Personalised follow-up messages at optimal intervals',
        icon: Zap
      },
      {
        title: 'Job Booking Integration',
        description: 'One-click booking from follow-up messages',
        icon: CheckCircle2
      }
    ],
    results: [
      {
        icon: TrendingUp,
        label: 'Quote Conversion Rate',
        value: '+55%'
      },
      {
        icon: CheckCircle2,
        label: 'Follow-up Consistency',
        value: '100%'
      },
      {
        icon: DollarSign,
        label: 'Monthly Revenue',
        value: '+75%'
      }
    ],
    testimonial: {
      quote: "The automated follow-ups have been a game-changer. We're converting way more quotes because we never forget to follow up anymore.",
      author: "Steve Martinez",
      role: "Plumbing Business Owner"
    },
    tags: ['Tradies', 'Quote Management', 'Workflow Automation', 'Follow-ups'],
    gradient: 'from-[#3B82F6] to-[#10B981]'
  },
  {
    id: 'ndis-support-coordination',
    title: 'NDIS Support Coordination System',
    subtitle: 'NDIS & Compliance Automation',
    description: 'Streamlining participant communication and plan management for an NDIS support coordinator.',
    challenge: 'Manual tracking of participant plans and communications was time-consuming and risked compliance issues with NDIS reporting requirements.',
    solution: 'Implemented automated participant communication system with plan tracking, appointment reminders, and compliance reporting.',
    approach: [
      {
        title: 'Participant Management',
        description: 'Automated tracking of participant plans and goals',
        icon: Users
      },
      {
        title: 'Communication Automation',
        description: 'Scheduled check-ins and appointment reminders',
        icon: Clock
      },
      {
        title: 'Compliance Reporting',
        description: 'Automated NDIS reporting and documentation',
        icon: Shield
      }
    ],
    results: [
      {
        icon: Clock,
        label: 'Admin Time Saved',
        value: '80%'
      },
      {
        icon: Shield,
        label: 'Compliance Accuracy',
        value: '100%'
      },
      {
        icon: Star,
        label: 'Participant Satisfaction',
        value: '94%'
      }
    ],
    testimonial: {
      quote: "The automated system has transformed how we manage participants. We're fully compliant and have more time to focus on what matters - supporting our participants.",
      author: "Rachel Thompson",
      role: "NDIS Support Coordinator"
    },
    tags: ['NDIS', 'Participant Management', 'Compliance', 'Automation'],
    gradient: 'from-[#F472B6] to-[#9333EA]'
  }
];

// Add JSON-LD structured data
const getJsonLd = (studies: typeof caseStudies) => ({
  '@context': 'https://schema.org',
  '@type': 'CollectionPage',
  headline: 'Melbourne Business Automation Success Stories',
  description: 'Discover how Melbourne businesses achieved success through our practical automation solutions.',
  publisher: {
    '@type': 'Organization',
    name: 'Advisync Solutions',
    url: 'https://advisync.com.au',
    logo: {
      '@type': 'ImageObject',
      url: 'https://advisync.com.au/images/advisync-logo.png'
    }
  },
  mainEntity: studies.map(study => ({
    '@type': 'Article',
    headline: study.title,
    description: study.description,
    articleBody: `${study.challenge}\n\n${study.solution}`,
    keywords: study.tags.join(', '),
    author: {
      '@type': 'Organization',
      name: 'Advisync Solutions'
    },
    publisher: {
      '@type': 'Organization',
      name: 'Advisync Solutions',
      logo: {
        '@type': 'ImageObject',
        url: 'https://advisync.com.au/images/advisync-logo.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': `https://advisync.com.au/case-studies/${study.id}`
    },
    about: {
      '@type': 'Thing',
      name: study.subtitle
    },
    ...(study.testimonial && {
      review: {
        '@type': 'Review',
        reviewBody: study.testimonial.quote,
        author: {
          '@type': 'Person',
          name: study.testimonial.author,
          jobTitle: study.testimonial.role
        }
      }
    })
  }))
});

export default function CaseStudiesPage() {
  return (
    <main className="py-12 md:py-20">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(getJsonLd(caseStudies)) }}
      />
      <Container>
        {/* Hero Section */}
        <section className="text-center mb-16 md:mb-32 relative px-4">
          <div className="absolute inset-0 bg-gradient-to-b from-primary/5 via-secondary/5 to-transparent rounded-3xl -z-10" />
          <h1 className="text-3xl sm:text-4xl md:text-6xl font-bold mb-6 md:mb-8 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary leading-tight">
            Success Stories
          </h1>
          <p className="text-base sm:text-lg md:text-xl text-foreground/60 max-w-3xl mx-auto leading-relaxed">
            Real results from tradies, healthcare providers, and NDIS services using our AI voice agents and workflow automations to capture more revenue and save time.
          </p>
        </section>

        {/* Case Studies */}
        <section className="space-y-16 md:space-y-32">
          {caseStudies.map((study, index) => (
            <article 
              key={study.id}
              className="group relative"
            >
              {/* Background Gradient */}
              <div className={`absolute inset-0 bg-gradient-to-br ${study.gradient} opacity-5 rounded-3xl -z-10`} />
              
              {/* Content */}
              <div className="relative bg-secondary/5 backdrop-blur-sm rounded-3xl p-6 md:p-12 border border-primary/10 hover:border-primary/20 transition-all duration-300">
                {/* Header */}
                <div className="mb-8 md:mb-12">
                  <span className="text-sm font-medium text-primary/80 mb-2 block">
                    {study.subtitle}
                  </span>
                  <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary leading-tight">
                    {study.title}
                  </h2>
                  <p className="text-base md:text-xl text-foreground/60 mb-8 max-w-3xl leading-relaxed">
                    {study.description}
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {study.tags.map((tag) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-primary/10 rounded-full text-sm text-primary"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Challenge & Solution */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 md:gap-12 mb-8 md:mb-12">
                  <div>
                    <h3 className="text-xl md:text-2xl font-bold mb-3 md:mb-4 flex items-center gap-2">
                      <span className="w-2 h-2 bg-primary rounded-full" />
                      The Challenge
                    </h3>
                    <p className="text-foreground/60 text-base md:text-lg leading-relaxed">{study.challenge}</p>
                  </div>
                  <div>
                    <h3 className="text-xl md:text-2xl font-bold mb-3 md:mb-4 flex items-center gap-2">
                      <span className="w-2 h-2 bg-secondary rounded-full" />
                      Our Solution
                    </h3>
                    <p className="text-foreground/60 text-base md:text-lg leading-relaxed">{study.solution}</p>
                  </div>
                </div>

                {/* Approach */}
                <div className="mb-8 md:mb-12">
                  <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-8">Our Approach</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
                    {study.approach.map((step) => (
                      <div 
                        key={step.title}
                        className="bg-secondary/5 rounded-2xl p-4 md:p-6"
                      >
                        <step.icon className="w-6 h-6 md:w-8 md:h-8 text-primary mb-3 md:mb-4" />
                        <h4 className="text-base md:text-lg font-semibold mb-3">{step.title}</h4>
                        <p className="text-sm md:text-base text-foreground/60 leading-relaxed">{step.description}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Results */}
                <div className="mb-8 md:mb-12">
                  <h3 className="text-xl md:text-2xl font-bold mb-4 md:mb-8">Key Results</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 md:gap-8">
                    {study.results.map((result) => (
                      <div 
                        key={result.label}
                        className="flex flex-col items-center text-center p-4 md:p-8 bg-secondary/5 rounded-2xl"
                      >
                        <result.icon className="w-6 h-6 md:w-8 md:h-8 text-primary mb-2 md:mb-4" />
                        <span className="text-2xl md:text-3xl font-bold text-accent-secondary mb-1 md:mb-2">
                          {result.value}
                        </span>
                        <span className="text-xs md:text-sm text-foreground/60">
                          {result.label}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Testimonial */}
                {study.testimonial && (
                  <div className="mb-8 md:mb-12">
                    <blockquote className="relative">
                      <div className="relative z-10">
                        <p className="text-base md:text-xl italic text-foreground/80 mb-4 md:mb-6 leading-relaxed">
                          "{study.testimonial.quote}"
                        </p>
                        <footer>
                          <p className="font-semibold">{study.testimonial.author}</p>
                          <p className="text-foreground/60 text-sm">{study.testimonial.role}</p>
                        </footer>
                      </div>
                      <div className="absolute top-0 left-0 transform -translate-x-2 -translate-y-2 md:-translate-x-4 md:-translate-y-4 text-4xl md:text-6xl text-primary/20">
                        "
                      </div>
                    </blockquote>
                  </div>
                )}

                {/* View Details Button */}
                <div className="flex justify-end">
                  <button className="group/btn inline-flex items-center gap-2 text-primary hover:text-primary/80 transition-colors">
                    <span>Read Full Case Study</span>
                    <ArrowRight className="w-4 h-4 transition-transform group-hover/btn:translate-x-1" />
                  </button>
                </div>
              </div>
            </article>
          ))}
        </section>

        {/* CTA Section */}
        <section className="mt-16 md:mt-32 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-secondary/5 to-primary/5 rounded-3xl -z-10" />
          <div className="text-center p-6 md:p-16">
            <h2 className="text-2xl md:text-4xl font-bold mb-4 md:mb-6 bg-clip-text text-transparent bg-gradient-to-r from-primary to-secondary">
              Ready to Transform Your Business?
            </h2>
            <p className="text-base md:text-xl text-foreground/60 mb-8 md:mb-12 max-w-2xl mx-auto">
              Let's create your success story together. Our team is ready to help you achieve your digital transformation goals.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4 md:gap-6">
              <a
                href="/contact"
                className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 rounded-full bg-primary text-white hover:bg-primary/90 transition-colors"
              >
                Start Your Project
              </a>
              <a
                href="/services"
                className="inline-flex items-center justify-center px-6 md:px-8 py-3 md:py-4 rounded-full bg-secondary/10 text-secondary hover:bg-secondary/20 transition-colors"
              >
                Explore Services
              </a>
            </div>
          </div>
        </section>
      </Container>
    </main>
  );
} 